{"ast": null, "code": "var _jsxFileName = \"C:\\\\customized\\\\ShouChuang Sporting\\\\\\u5EFA\\u7AD9\\u8D44\\u6599\\u6536\\u96C6\\uFF08\\u6700\\u65B0\\u7248\\uFF09\\\\shouchuang-website\\\\src\\\\components\\\\VideoPlayer.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { Button, Spin } from 'antd';\nimport { PlayCircleOutlined, LoadingOutlined } from '@ant-design/icons';\nimport { getImagePath } from '../utils/imageUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoPlayer = ({\n  src,\n  poster,\n  className = '',\n  style = {}\n}) => {\n  _s();\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showControls, setShowControls] = useState(true);\n  const videoRef = useRef(null);\n  const handlePlayPause = () => {\n    if (videoRef.current) {\n      if (isPlaying) {\n        videoRef.current.pause();\n      } else {\n        setIsLoading(true);\n        videoRef.current.play();\n      }\n    }\n  };\n  const handlePlay = () => {\n    setIsPlaying(true);\n    setIsLoading(false);\n  };\n  const handlePause = () => {\n    setIsPlaying(false);\n    setIsLoading(false);\n  };\n  const handleLoadStart = () => {\n    setIsLoading(true);\n  };\n  const handleCanPlay = () => {\n    setIsLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `video-container ${className}`,\n    style: style,\n    onMouseEnter: () => setShowControls(true),\n    onMouseLeave: () => setShowControls(true),\n    children: [/*#__PURE__*/_jsxDEV(\"video\", {\n      ref: videoRef,\n      poster: poster ? getImagePath(poster) : undefined,\n      preload: \"metadata\",\n      onPlay: handlePlay,\n      onPause: handlePause,\n      onLoadStart: handleLoadStart,\n      onCanPlay: handleCanPlay,\n      controls: showControls,\n      children: [/*#__PURE__*/_jsxDEV(\"source\", {\n        src: getImagePath(src),\n        type: \"video/mp4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), \"Your browser does not support the video tag.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), !isPlaying && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '50%',\n        left: '50%',\n        transform: 'translate(-50%, -50%)',\n        zIndex: 10,\n        pointerEvents: isLoading ? 'none' : 'auto'\n      },\n      children: isLoading ? /*#__PURE__*/_jsxDEV(Spin, {\n        indicator: /*#__PURE__*/_jsxDEV(LoadingOutlined, {\n          style: {\n            fontSize: 48,\n            color: '#fff'\n          },\n          spin: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 26\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        shape: \"circle\",\n        size: \"large\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 21\n        }, this),\n        onClick: handlePlayPause,\n        style: {\n          width: 64,\n          height: 64,\n          fontSize: 24,\n          backgroundColor: 'rgba(0, 86, 179, 0.8)',\n          borderColor: 'rgba(0, 86, 179, 0.8)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoPlayer, \"R0WPupbMHGYgS4rm4aFA0mC99uI=\");\n_c = VideoPlayer;\nexport default VideoPlayer;\nvar _c;\n$RefreshReg$(_c, \"VideoPlayer\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "<PERSON><PERSON>", "Spin", "PlayCircleOutlined", "LoadingOutlined", "getImagePath", "jsxDEV", "_jsxDEV", "VideoPlayer", "src", "poster", "className", "style", "_s", "isPlaying", "setIsPlaying", "isLoading", "setIsLoading", "showControls", "setShowControls", "videoRef", "handlePlayPause", "current", "pause", "play", "handlePlay", "handlePause", "handleLoadStart", "handleCanPlay", "onMouseEnter", "onMouseLeave", "children", "ref", "undefined", "preload", "onPlay", "onPause", "onLoadStart", "onCanPlay", "controls", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "top", "left", "transform", "zIndex", "pointerEvents", "indicator", "fontSize", "color", "spin", "shape", "size", "icon", "onClick", "width", "height", "backgroundColor", "borderColor", "display", "alignItems", "justifyContent", "_c", "$RefreshReg$"], "sources": ["C:/customized/ShouChuang Sporting/建站资料收集（最新版）/shouchuang-website/src/components/VideoPlayer.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { <PERSON><PERSON>, Spin } from 'antd';\nimport { PlayCircleOutlined, PauseCircleOutlined, LoadingOutlined } from '@ant-design/icons';\nimport { getImagePath } from '../utils/imageUtils';\n\ninterface VideoPlayerProps {\n  src: string;\n  poster?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst VideoPlayer: React.FC<VideoPlayerProps> = ({ \n  src, \n  poster, \n  className = '', \n  style = {} \n}) => {\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showControls, setShowControls] = useState(true);\n  const videoRef = useRef<HTMLVideoElement>(null);\n\n  const handlePlayPause = () => {\n    if (videoRef.current) {\n      if (isPlaying) {\n        videoRef.current.pause();\n      } else {\n        setIsLoading(true);\n        videoRef.current.play();\n      }\n    }\n  };\n\n  const handlePlay = () => {\n    setIsPlaying(true);\n    setIsLoading(false);\n  };\n\n  const handlePause = () => {\n    setIsPlaying(false);\n    setIsLoading(false);\n  };\n\n  const handleLoadStart = () => {\n    setIsLoading(true);\n  };\n\n  const handleCanPlay = () => {\n    setIsLoading(false);\n  };\n\n  return (\n    <div \n      className={`video-container ${className}`}\n      style={style}\n      onMouseEnter={() => setShowControls(true)}\n      onMouseLeave={() => setShowControls(true)}\n    >\n      <video\n        ref={videoRef}\n        poster={poster ? getImagePath(poster) : undefined}\n        preload=\"metadata\"\n        onPlay={handlePlay}\n        onPause={handlePause}\n        onLoadStart={handleLoadStart}\n        onCanPlay={handleCanPlay}\n        controls={showControls}\n      >\n        <source src={getImagePath(src)} type=\"video/mp4\" />\n        Your browser does not support the video tag.\n      </video>\n      \n      {/* 自定义播放按钮覆盖层 */}\n      {!isPlaying && (\n        <div \n          style={{\n            position: 'absolute',\n            top: '50%',\n            left: '50%',\n            transform: 'translate(-50%, -50%)',\n            zIndex: 10,\n            pointerEvents: isLoading ? 'none' : 'auto'\n          }}\n        >\n          {isLoading ? (\n            <Spin \n              indicator={<LoadingOutlined style={{ fontSize: 48, color: '#fff' }} spin />} \n            />\n          ) : (\n            <Button\n              type=\"primary\"\n              shape=\"circle\"\n              size=\"large\"\n              icon={<PlayCircleOutlined />}\n              onClick={handlePlayPause}\n              style={{\n                width: 64,\n                height: 64,\n                fontSize: 24,\n                backgroundColor: 'rgba(0, 86, 179, 0.8)',\n                borderColor: 'rgba(0, 86, 179, 0.8)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}\n            />\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default VideoPlayer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,IAAI,QAAQ,MAAM;AACnC,SAASC,kBAAkB,EAAuBC,eAAe,QAAQ,mBAAmB;AAC5F,SAASC,YAAY,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASnD,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,GAAG;EACHC,MAAM;EACNC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC;AACX,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMqB,QAAQ,GAAGpB,MAAM,CAAmB,IAAI,CAAC;EAE/C,MAAMqB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAID,QAAQ,CAACE,OAAO,EAAE;MACpB,IAAIR,SAAS,EAAE;QACbM,QAAQ,CAACE,OAAO,CAACC,KAAK,CAAC,CAAC;MAC1B,CAAC,MAAM;QACLN,YAAY,CAAC,IAAI,CAAC;QAClBG,QAAQ,CAACE,OAAO,CAACE,IAAI,CAAC,CAAC;MACzB;IACF;EACF,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBV,YAAY,CAAC,IAAI,CAAC;IAClBE,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMS,WAAW,GAAGA,CAAA,KAAM;IACxBX,YAAY,CAAC,KAAK,CAAC;IACnBE,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5BV,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMW,aAAa,GAAGA,CAAA,KAAM;IAC1BX,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACEV,OAAA;IACEI,SAAS,EAAE,mBAAmBA,SAAS,EAAG;IAC1CC,KAAK,EAAEA,KAAM;IACbiB,YAAY,EAAEA,CAAA,KAAMV,eAAe,CAAC,IAAI,CAAE;IAC1CW,YAAY,EAAEA,CAAA,KAAMX,eAAe,CAAC,IAAI,CAAE;IAAAY,QAAA,gBAE1CxB,OAAA;MACEyB,GAAG,EAAEZ,QAAS;MACdV,MAAM,EAAEA,MAAM,GAAGL,YAAY,CAACK,MAAM,CAAC,GAAGuB,SAAU;MAClDC,OAAO,EAAC,UAAU;MAClBC,MAAM,EAAEV,UAAW;MACnBW,OAAO,EAAEV,WAAY;MACrBW,WAAW,EAAEV,eAAgB;MAC7BW,SAAS,EAAEV,aAAc;MACzBW,QAAQ,EAAErB,YAAa;MAAAa,QAAA,gBAEvBxB,OAAA;QAAQE,GAAG,EAAEJ,YAAY,CAACI,GAAG,CAAE;QAAC+B,IAAI,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gDAErD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,EAGP,CAAC9B,SAAS,iBACTP,OAAA;MACEK,KAAK,EAAE;QACLiC,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,KAAK;QACVC,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,uBAAuB;QAClCC,MAAM,EAAE,EAAE;QACVC,aAAa,EAAElC,SAAS,GAAG,MAAM,GAAG;MACtC,CAAE;MAAAe,QAAA,EAEDf,SAAS,gBACRT,OAAA,CAACL,IAAI;QACHiD,SAAS,eAAE5C,OAAA,CAACH,eAAe;UAACQ,KAAK,EAAE;YAAEwC,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAACC,IAAI;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC,gBAEFrC,OAAA,CAACN,MAAM;QACLuC,IAAI,EAAC,SAAS;QACde,KAAK,EAAC,QAAQ;QACdC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAElD,OAAA,CAACJ,kBAAkB;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7Bc,OAAO,EAAErC,eAAgB;QACzBT,KAAK,EAAE;UACL+C,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVR,QAAQ,EAAE,EAAE;UACZS,eAAe,EAAE,uBAAuB;UACxCC,WAAW,EAAE,uBAAuB;UACpCC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE;QAClB;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/B,EAAA,CApGIL,WAAuC;AAAA0D,EAAA,GAAvC1D,WAAuC;AAsG7C,eAAeA,WAAW;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}