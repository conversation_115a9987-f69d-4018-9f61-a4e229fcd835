{"ast": null, "code": "var _jsxFileName = \"C:\\\\customized\\\\ShouChuang Sporting\\\\\\u5EFA\\u7AD9\\u8D44\\u6599\\u6536\\u96C6\\uFF08\\u6700\\u65B0\\u7248\\uFF09\\\\shouchuang-website\\\\src\\\\components\\\\VideoPlayer.tsx\";\nimport React from 'react';\nimport { getImagePath } from '../utils/imageUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoPlayer = ({\n  src,\n  poster,\n  className = '',\n  style = {}\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `video-container ${className}`,\n    style: style,\n    children: /*#__PURE__*/_jsxDEV(\"video\", {\n      controls: true,\n      poster: poster ? getImagePath(poster) : undefined,\n      preload: \"metadata\",\n      style: {\n        width: '100%',\n        height: 'auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"source\", {\n        src: getImagePath(src),\n        type: \"video/mp4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), \"Your browser does not support the video tag.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_c = VideoPlayer;\nexport default VideoPlayer;\nvar _c;\n$RefreshReg$(_c, \"VideoPlayer\");", "map": {"version": 3, "names": ["React", "getImagePath", "jsxDEV", "_jsxDEV", "VideoPlayer", "src", "poster", "className", "style", "children", "controls", "undefined", "preload", "width", "height", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/customized/ShouChuang Sporting/建站资料收集（最新版）/shouchuang-website/src/components/VideoPlayer.tsx"], "sourcesContent": ["import React from 'react';\nimport { getImagePath } from '../utils/imageUtils';\n\ninterface VideoPlayerProps {\n  src: string;\n  poster?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst VideoPlayer: React.FC<VideoPlayerProps> = ({\n  src,\n  poster,\n  className = '',\n  style = {}\n}) => {\n  return (\n    <div\n      className={`video-container ${className}`}\n      style={style}\n    >\n      <video\n        controls\n        poster={poster ? getImagePath(poster) : undefined}\n        preload=\"metadata\"\n        style={{ width: '100%', height: 'auto' }}\n      >\n        <source src={getImagePath(src)} type=\"video/mp4\" />\n        Your browser does not support the video tag.\n      </video>\n    </div>\n  );\n};\n\nexport default VideoPlayer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASnD,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,GAAG;EACHC,MAAM;EACNC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC;AACX,CAAC,KAAK;EACJ,oBACEL,OAAA;IACEI,SAAS,EAAE,mBAAmBA,SAAS,EAAG;IAC1CC,KAAK,EAAEA,KAAM;IAAAC,QAAA,eAEbN,OAAA;MACEO,QAAQ;MACRJ,MAAM,EAAEA,MAAM,GAAGL,YAAY,CAACK,MAAM,CAAC,GAAGK,SAAU;MAClDC,OAAO,EAAC,UAAU;MAClBJ,KAAK,EAAE;QAAEK,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAL,QAAA,gBAEzCN,OAAA;QAAQE,GAAG,EAAEJ,YAAY,CAACI,GAAG,CAAE;QAACU,IAAI,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gDAErD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACC,EAAA,GAtBIhB,WAAuC;AAwB7C,eAAeA,WAAW;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}