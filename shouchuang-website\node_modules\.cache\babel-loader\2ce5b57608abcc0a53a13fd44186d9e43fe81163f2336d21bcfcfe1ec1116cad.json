{"ast": null, "code": "var _jsxFileName = \"C:\\\\customized\\\\ShouChuang Sporting\\\\\\u5EFA\\u7AD9\\u8D44\\u6599\\u6536\\u96C6\\uFF08\\u6700\\u65B0\\u7248\\uFF09\\\\shouchuang-website\\\\src\\\\pages\\\\Contact.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Typography, Row, Col, Form, Input, Button, Card, Space, Divider, Select, message } from 'antd';\nimport { EnvironmentOutlined, PhoneOutlined, MailOutlined, WhatsAppOutlined, SendOutlined } from '@ant-design/icons';\nimport { contactInfo } from '../models/mockData';\nimport WhatsAppContact from '../components/WhatsAppContact';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst ContactPage = () => {\n  _s();\n  const [form] = Form.useForm();\n  const onFinish = values => {\n    console.log('Form values:', values);\n    message.success('Your message has been sent successfully! We will contact you soon.');\n    form.resetFields();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container contact-page-red\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"contact-header\",\n      style: {\n        textAlign: 'center',\n        marginBottom: 48\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        className: \"contact-title\",\n        children: \"Contact Us\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n        className: \"contact-subtitle\",\n        style: {\n          fontSize: 16,\n          maxWidth: 800,\n          margin: '0 auto'\n        },\n        children: \"Get in touch with our team to discuss your helmet manufacturing needs or request a quote for your project.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section contact-info-section\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"contact-info-card\",\n            style: {\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Space, {\n              align: \"center\",\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(EnvironmentOutlined, {\n                className: \"contact-icon\",\n                style: {\n                  fontSize: 24\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                style: {\n                  margin: 0\n                },\n                children: \"Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                fontSize: 16\n              },\n              children: contactInfo.address\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"contact-info-card\",\n            style: {\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Space, {\n              align: \"center\",\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(PhoneOutlined, {\n                className: \"contact-icon\",\n                style: {\n                  fontSize: 24\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                style: {\n                  margin: 0\n                },\n                children: \"Phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                fontSize: 16\n              },\n              children: contactInfo.phone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"contact-info-card\",\n            style: {\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Space, {\n              align: \"center\",\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(MailOutlined, {\n                className: \"contact-icon\",\n                style: {\n                  fontSize: 24\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                style: {\n                  margin: 0\n                },\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                fontSize: 16\n              },\n              children: contactInfo.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section contact-form-section\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [48, 48],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 14,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            className: \"form-title\",\n            children: \"Send Us a Message\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            className: \"form-description\",\n            style: {\n              marginBottom: 24\n            },\n            children: \"Fill out the form below to get in touch with our team. We'll respond to your inquiry as soon as possible.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            name: \"contact_form\",\n            layout: \"vertical\",\n            onFinish: onFinish,\n            requiredMark: false,\n            className: \"contact-form-red\",\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"name\",\n                  label: \"Your Name\",\n                  rules: [{\n                    required: true,\n                    message: 'Please enter your name'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    size: \"large\",\n                    placeholder: \"Enter your name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"email\",\n                  label: \"Email Address\",\n                  rules: [{\n                    required: true,\n                    message: 'Please enter your email'\n                  }, {\n                    type: 'email',\n                    message: 'Please enter a valid email'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    size: \"large\",\n                    placeholder: \"Enter your email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 112,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"phone\",\n                  label: \"Phone Number\",\n                  rules: [{\n                    required: true,\n                    message: 'Please enter your phone number'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    size: \"large\",\n                    placeholder: \"Enter your phone number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"subject\",\n                  label: \"Subject\",\n                  rules: [{\n                    required: true,\n                    message: 'Please select a subject'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    size: \"large\",\n                    placeholder: \"Select a subject\",\n                    children: [/*#__PURE__*/_jsxDEV(Option, {\n                      value: \"general\",\n                      children: \"General Inquiry\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"quote\",\n                      children: \"Request a Quote\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"oem\",\n                      children: \"OEM Services\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 135,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"odm\",\n                      children: \"ODM Services\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"sample\",\n                      children: \"Sample Request\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"other\",\n                      children: \"Other\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 138,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"message\",\n              label: \"Message\",\n              rules: [{\n                required: true,\n                message: 'Please enter your message'\n              }],\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 6,\n                placeholder: \"Enter your message or inquiry details\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                size: \"large\",\n                icon: /*#__PURE__*/_jsxDEV(SendOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 25\n                }, this),\n                className: \"contact-submit-btn\",\n                style: {\n                  minWidth: 150\n                },\n                children: \"Send Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 10,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            children: \"Our Location\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 24\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/images/contact/map.jpg\",\n              alt: \"Company Location Map\",\n              style: {\n                width: '100%',\n                borderRadius: 8\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              children: \"Connect on WhatsApp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              align: \"middle\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 16,\n                children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                  style: {\n                    marginBottom: 0\n                  },\n                  children: \"Scan the QR code or click the button below to connect with us on WhatsApp for quick responses to your inquiries.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  icon: /*#__PURE__*/_jsxDEV(WhatsAppOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 27\n                  }, this),\n                  style: {\n                    marginTop: 16,\n                    background: '#25D366',\n                    borderColor: '#25D366'\n                  },\n                  href: `https://wa.me/${contactInfo.whatsapp.replace(/[^0-9]/g, '')}`,\n                  target: \"_blank\",\n                  children: \"Chat on WhatsApp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 8,\n                style: {\n                  textAlign: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(WhatsAppContact, {\n                  imageSrc: \"/images/whatApp.jpg\",\n                  altText: \"WhatsApp QR Code\",\n                  description: \"\",\n                  className: \"contact-whatsapp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          textAlign: 'center',\n          marginBottom: 32\n        },\n        children: \"Frequently Asked Questions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            children: \"What information should I provide for a quote?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            children: \"To provide an accurate quote, please include details such as the type of helmet, quantity, specific features, customization requirements, and target market (for certification purposes).\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            children: \"How can I request samples?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            children: \"You can request samples by contacting us through the form above or via email. Please specify the helmet model, quantity of samples needed, and any customization requirements.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            children: \"What is your typical response time?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            children: \"We typically respond to inquiries within 24-48 hours during business days. For urgent matters, we recommend contacting us via phone or WhatsApp.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            children: \"Do you offer factory tours?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            children: \"Yes, we welcome clients to visit our factory. Please contact us in advance to schedule a tour so we can make proper arrangements for your visit.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      style: {\n        background: '#f5f5f5',\n        padding: 32,\n        borderRadius: 8,\n        marginTop: 48,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        children: \"Ready to Start Your Project?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n        style: {\n          fontSize: 16,\n          maxWidth: 800,\n          margin: '0 auto 24px'\n        },\n        children: \"Contact us today to discuss your helmet manufacturing needs and discover how we can help bring your ideas to life. Our team is ready to provide you with the information and support you need to make your project a success.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        size: \"large\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"large\",\n          icon: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 53\n          }, this),\n          children: \"Email Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"large\",\n          icon: /*#__PURE__*/_jsxDEV(PhoneOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 38\n          }, this),\n          children: \"Call Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(ContactPage, \"rI7DrJIrFu7YmlGWYiMFTzs8jF0=\", false, function () {\n  return [Form.useForm];\n});\n_c = ContactPage;\nexport default ContactPage;\nvar _c;\n$RefreshReg$(_c, \"ContactPage\");", "map": {"version": 3, "names": ["React", "Typography", "Row", "Col", "Form", "Input", "<PERSON><PERSON>", "Card", "Space", "Divider", "Select", "message", "EnvironmentOutlined", "PhoneOutlined", "MailOutlined", "WhatsAppOutlined", "SendOutlined", "contactInfo", "WhatsAppContact", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "TextArea", "Option", "ContactPage", "_s", "form", "useForm", "onFinish", "values", "console", "log", "success", "resetFields", "className", "children", "style", "textAlign", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "max<PERSON><PERSON><PERSON>", "margin", "gutter", "xs", "md", "height", "align", "level", "address", "phone", "email", "lg", "name", "layout", "requiredMark", "<PERSON><PERSON>", "label", "rules", "required", "size", "placeholder", "type", "value", "rows", "htmlType", "icon", "min<PERSON><PERSON><PERSON>", "src", "alt", "width", "borderRadius", "marginTop", "background", "borderColor", "href", "whatsapp", "replace", "target", "imageSrc", "altText", "description", "padding", "_c", "$RefreshReg$"], "sources": ["C:/customized/ShouChuang Sporting/建站资料收集（最新版）/shouchuang-website/src/pages/Contact.tsx"], "sourcesContent": ["import React from 'react';\nimport { Typography, Row, Col, Form, Input, Button, Card, Space, Divider, Select, message } from 'antd';\nimport {\n  EnvironmentOutlined,\n  PhoneOutlined,\n  MailOutlined,\n  WhatsAppOutlined,\n  SendOutlined\n} from '@ant-design/icons';\nimport { contactInfo } from '../models/mockData';\nimport WhatsAppContact from '../components/WhatsAppContact';\n\nconst { Title, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst ContactPage: React.FC = () => {\n  const [form] = Form.useForm();\n\n  const onFinish = (values: any) => {\n    console.log('Form values:', values);\n    message.success('Your message has been sent successfully! We will contact you soon.');\n    form.resetFields();\n  };\n\n  return (\n    <div className=\"page-container contact-page-red\">\n      {/* Page Header */}\n      <div className=\"contact-header\" style={{ textAlign: 'center', marginBottom: 48 }}>\n        <Title className=\"contact-title\">Contact Us</Title>\n        <Paragraph className=\"contact-subtitle\" style={{ fontSize: 16, maxWidth: 800, margin: '0 auto' }}>\n          Get in touch with our team to discuss your helmet manufacturing needs or request a quote for your project.\n        </Paragraph>\n      </div>\n\n      {/* Contact Information */}\n      <section className=\"section contact-info-section\">\n        <Row gutter={[24, 24]}>\n          <Col xs={24} md={8}>\n            <Card className=\"contact-info-card\" style={{ height: '100%' }}>\n              <Space align=\"center\" style={{ marginBottom: 16 }}>\n                <EnvironmentOutlined className=\"contact-icon\" style={{ fontSize: 24 }} />\n                <Title level={4} style={{ margin: 0 }}>Address</Title>\n              </Space>\n              <Paragraph style={{ fontSize: 16 }}>\n                {contactInfo.address}\n              </Paragraph>\n            </Card>\n          </Col>\n          <Col xs={24} md={8}>\n            <Card className=\"contact-info-card\" style={{ height: '100%' }}>\n              <Space align=\"center\" style={{ marginBottom: 16 }}>\n                <PhoneOutlined className=\"contact-icon\" style={{ fontSize: 24 }} />\n                <Title level={4} style={{ margin: 0 }}>Phone</Title>\n              </Space>\n              <Paragraph style={{ fontSize: 16 }}>\n                {contactInfo.phone}\n              </Paragraph>\n            </Card>\n          </Col>\n          <Col xs={24} md={8}>\n            <Card className=\"contact-info-card\" style={{ height: '100%' }}>\n              <Space align=\"center\" style={{ marginBottom: 16 }}>\n                <MailOutlined className=\"contact-icon\" style={{ fontSize: 24 }} />\n                <Title level={4} style={{ margin: 0 }}>Email</Title>\n              </Space>\n              <Paragraph style={{ fontSize: 16 }}>\n                {contactInfo.email}\n              </Paragraph>\n            </Card>\n          </Col>\n        </Row>\n      </section>\n\n      <Divider />\n\n      {/* Contact Form and Map */}\n      <section className=\"section contact-form-section\">\n        <Row gutter={[48, 48]}>\n          <Col xs={24} lg={14}>\n            <Title level={2} className=\"form-title\">Send Us a Message</Title>\n            <Paragraph className=\"form-description\" style={{ marginBottom: 24 }}>\n              Fill out the form below to get in touch with our team. We'll respond to your inquiry as soon as possible.\n            </Paragraph>\n            <Form\n              form={form}\n              name=\"contact_form\"\n              layout=\"vertical\"\n              onFinish={onFinish}\n              requiredMark={false}\n              className=\"contact-form-red\"\n            >\n              <Row gutter={16}>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name=\"name\"\n                    label=\"Your Name\"\n                    rules={[{ required: true, message: 'Please enter your name' }]}\n                  >\n                    <Input size=\"large\" placeholder=\"Enter your name\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name=\"email\"\n                    label=\"Email Address\"\n                    rules={[\n                      { required: true, message: 'Please enter your email' },\n                      { type: 'email', message: 'Please enter a valid email' }\n                    ]}\n                  >\n                    <Input size=\"large\" placeholder=\"Enter your email\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n              <Row gutter={16}>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name=\"phone\"\n                    label=\"Phone Number\"\n                    rules={[{ required: true, message: 'Please enter your phone number' }]}\n                  >\n                    <Input size=\"large\" placeholder=\"Enter your phone number\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name=\"subject\"\n                    label=\"Subject\"\n                    rules={[{ required: true, message: 'Please select a subject' }]}\n                  >\n                    <Select size=\"large\" placeholder=\"Select a subject\">\n                      <Option value=\"general\">General Inquiry</Option>\n                      <Option value=\"quote\">Request a Quote</Option>\n                      <Option value=\"oem\">OEM Services</Option>\n                      <Option value=\"odm\">ODM Services</Option>\n                      <Option value=\"sample\">Sample Request</Option>\n                      <Option value=\"other\">Other</Option>\n                    </Select>\n                  </Form.Item>\n                </Col>\n              </Row>\n              <Form.Item\n                name=\"message\"\n                label=\"Message\"\n                rules={[{ required: true, message: 'Please enter your message' }]}\n              >\n                <TextArea \n                  rows={6} \n                  placeholder=\"Enter your message or inquiry details\" \n                  size=\"large\" \n                />\n              </Form.Item>\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  size=\"large\"\n                  icon={<SendOutlined />}\n                  className=\"contact-submit-btn\"\n                  style={{ minWidth: 150 }}\n                >\n                  Send Message\n                </Button>\n              </Form.Item>\n            </Form>\n          </Col>\n          <Col xs={24} lg={10}>\n            <Title level={2}>Our Location</Title>\n            <div style={{ marginBottom: 24 }}>\n              <img \n                src=\"/images/contact/map.jpg\" \n                alt=\"Company Location Map\" \n                style={{ width: '100%', borderRadius: 8 }} \n              />\n            </div>\n            <Card>\n              <Title level={4}>Connect on WhatsApp</Title>\n              <Row gutter={16} align=\"middle\">\n                <Col xs={24} md={16}>\n                  <Paragraph style={{ marginBottom: 0 }}>\n                    Scan the QR code or click the button below to connect with us on WhatsApp for quick responses to your inquiries.\n                  </Paragraph>\n                  <Button\n                    type=\"primary\"\n                    icon={<WhatsAppOutlined />}\n                    style={{ marginTop: 16, background: '#25D366', borderColor: '#25D366' }}\n                    href={`https://wa.me/${contactInfo.whatsapp.replace(/[^0-9]/g, '')}`}\n                    target=\"_blank\"\n                  >\n                    Chat on WhatsApp\n                  </Button>\n                </Col>\n                <Col xs={24} md={8} style={{ textAlign: 'center' }}>\n                  <WhatsAppContact\n                    imageSrc=\"/images/whatApp.jpg\"\n                    altText=\"WhatsApp QR Code\"\n                    description=\"\"\n                    className=\"contact-whatsapp\"\n                  />\n                </Col>\n              </Row>\n            </Card>\n          </Col>\n        </Row>\n      </section>\n\n      <Divider />\n\n      {/* FAQ Section */}\n      <section className=\"section\">\n        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>Frequently Asked Questions</Title>\n        <Row gutter={[24, 24]}>\n          <Col xs={24} md={12}>\n            <Title level={4}>What information should I provide for a quote?</Title>\n            <Paragraph>\n              To provide an accurate quote, please include details such as the type of helmet, quantity, specific features, customization requirements, and target market (for certification purposes).\n            </Paragraph>\n          </Col>\n          <Col xs={24} md={12}>\n            <Title level={4}>How can I request samples?</Title>\n            <Paragraph>\n              You can request samples by contacting us through the form above or via email. Please specify the helmet model, quantity of samples needed, and any customization requirements.\n            </Paragraph>\n          </Col>\n          <Col xs={24} md={12}>\n            <Title level={4}>What is your typical response time?</Title>\n            <Paragraph>\n              We typically respond to inquiries within 24-48 hours during business days. For urgent matters, we recommend contacting us via phone or WhatsApp.\n            </Paragraph>\n          </Col>\n          <Col xs={24} md={12}>\n            <Title level={4}>Do you offer factory tours?</Title>\n            <Paragraph>\n              Yes, we welcome clients to visit our factory. Please contact us in advance to schedule a tour so we can make proper arrangements for your visit.\n            </Paragraph>\n          </Col>\n        </Row>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"section\" style={{ background: '#f5f5f5', padding: 32, borderRadius: 8, marginTop: 48, textAlign: 'center' }}>\n        <Title level={2}>Ready to Start Your Project?</Title>\n        <Paragraph style={{ fontSize: 16, maxWidth: 800, margin: '0 auto 24px' }}>\n          Contact us today to discuss your helmet manufacturing needs and discover how we can help bring your ideas to life.\n          Our team is ready to provide you with the information and support you need to make your project a success.\n        </Paragraph>\n        <Space size=\"large\">\n          <Button type=\"primary\" size=\"large\" icon={<MailOutlined />}>\n            Email Us\n          </Button>\n          <Button size=\"large\" icon={<PhoneOutlined />}>\n            Call Us\n          </Button>\n        </Space>\n      </section>\n    </div>\n  );\n};\n\nexport default ContactPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACvG,SACEC,mBAAmB,EACnBC,aAAa,EACbC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,QACP,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,eAAe,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAM;EAAEC,KAAK;EAAEC;AAAU,CAAC,GAAGrB,UAAU;AACvC,MAAM;EAAEsB;AAAS,CAAC,GAAGlB,KAAK;AAC1B,MAAM;EAAEmB;AAAO,CAAC,GAAGd,MAAM;AAEzB,MAAMe,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,IAAI,CAAC,GAAGvB,IAAI,CAACwB,OAAO,CAAC,CAAC;EAE7B,MAAMC,QAAQ,GAAIC,MAAW,IAAK;IAChCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,MAAM,CAAC;IACnCnB,OAAO,CAACsB,OAAO,CAAC,oEAAoE,CAAC;IACrFN,IAAI,CAACO,WAAW,CAAC,CAAC;EACpB,CAAC;EAED,oBACEd,OAAA;IAAKe,SAAS,EAAC,iCAAiC;IAAAC,QAAA,gBAE9ChB,OAAA;MAAKe,SAAS,EAAC,gBAAgB;MAACE,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBAC/EhB,OAAA,CAACC,KAAK;QAACc,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnDvB,OAAA,CAACE,SAAS;QAACa,SAAS,EAAC,kBAAkB;QAACE,KAAK,EAAE;UAAEO,QAAQ,EAAE,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAV,QAAA,EAAC;MAElG;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGNvB,OAAA;MAASe,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC/ChB,OAAA,CAAClB,GAAG;QAAC6C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACpBhB,OAAA,CAACjB,GAAG;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,eACjBhB,OAAA,CAACb,IAAI;YAAC4B,SAAS,EAAC,mBAAmB;YAACE,KAAK,EAAE;cAAEa,MAAM,EAAE;YAAO,CAAE;YAAAd,QAAA,gBAC5DhB,OAAA,CAACZ,KAAK;cAAC2C,KAAK,EAAC,QAAQ;cAACd,KAAK,EAAE;gBAAEE,YAAY,EAAE;cAAG,CAAE;cAAAH,QAAA,gBAChDhB,OAAA,CAACR,mBAAmB;gBAACuB,SAAS,EAAC,cAAc;gBAACE,KAAK,EAAE;kBAAEO,QAAQ,EAAE;gBAAG;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzEvB,OAAA,CAACC,KAAK;gBAAC+B,KAAK,EAAE,CAAE;gBAACf,KAAK,EAAE;kBAAES,MAAM,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EAAC;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACRvB,OAAA,CAACE,SAAS;cAACe,KAAK,EAAE;gBAAEO,QAAQ,EAAE;cAAG,CAAE;cAAAR,QAAA,EAChCnB,WAAW,CAACoC;YAAO;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvB,OAAA,CAACjB,GAAG;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,eACjBhB,OAAA,CAACb,IAAI;YAAC4B,SAAS,EAAC,mBAAmB;YAACE,KAAK,EAAE;cAAEa,MAAM,EAAE;YAAO,CAAE;YAAAd,QAAA,gBAC5DhB,OAAA,CAACZ,KAAK;cAAC2C,KAAK,EAAC,QAAQ;cAACd,KAAK,EAAE;gBAAEE,YAAY,EAAE;cAAG,CAAE;cAAAH,QAAA,gBAChDhB,OAAA,CAACP,aAAa;gBAACsB,SAAS,EAAC,cAAc;gBAACE,KAAK,EAAE;kBAAEO,QAAQ,EAAE;gBAAG;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnEvB,OAAA,CAACC,KAAK;gBAAC+B,KAAK,EAAE,CAAE;gBAACf,KAAK,EAAE;kBAAES,MAAM,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACRvB,OAAA,CAACE,SAAS;cAACe,KAAK,EAAE;gBAAEO,QAAQ,EAAE;cAAG,CAAE;cAAAR,QAAA,EAChCnB,WAAW,CAACqC;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvB,OAAA,CAACjB,GAAG;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,eACjBhB,OAAA,CAACb,IAAI;YAAC4B,SAAS,EAAC,mBAAmB;YAACE,KAAK,EAAE;cAAEa,MAAM,EAAE;YAAO,CAAE;YAAAd,QAAA,gBAC5DhB,OAAA,CAACZ,KAAK;cAAC2C,KAAK,EAAC,QAAQ;cAACd,KAAK,EAAE;gBAAEE,YAAY,EAAE;cAAG,CAAE;cAAAH,QAAA,gBAChDhB,OAAA,CAACN,YAAY;gBAACqB,SAAS,EAAC,cAAc;gBAACE,KAAK,EAAE;kBAAEO,QAAQ,EAAE;gBAAG;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClEvB,OAAA,CAACC,KAAK;gBAAC+B,KAAK,EAAE,CAAE;gBAACf,KAAK,EAAE;kBAAES,MAAM,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACRvB,OAAA,CAACE,SAAS;cAACe,KAAK,EAAE;gBAAEO,QAAQ,EAAE;cAAG,CAAE;cAAAR,QAAA,EAChCnB,WAAW,CAACsC;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVvB,OAAA,CAACX,OAAO;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXvB,OAAA;MAASe,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC/ChB,OAAA,CAAClB,GAAG;QAAC6C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACpBhB,OAAA,CAACjB,GAAG;UAAC6C,EAAE,EAAE,EAAG;UAACQ,EAAE,EAAE,EAAG;UAAApB,QAAA,gBAClBhB,OAAA,CAACC,KAAK;YAAC+B,KAAK,EAAE,CAAE;YAACjB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjEvB,OAAA,CAACE,SAAS;YAACa,SAAS,EAAC,kBAAkB;YAACE,KAAK,EAAE;cAAEE,YAAY,EAAE;YAAG,CAAE;YAAAH,QAAA,EAAC;UAErE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZvB,OAAA,CAAChB,IAAI;YACHuB,IAAI,EAAEA,IAAK;YACX8B,IAAI,EAAC,cAAc;YACnBC,MAAM,EAAC,UAAU;YACjB7B,QAAQ,EAAEA,QAAS;YACnB8B,YAAY,EAAE,KAAM;YACpBxB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAE5BhB,OAAA,CAAClB,GAAG;cAAC6C,MAAM,EAAE,EAAG;cAAAX,QAAA,gBACdhB,OAAA,CAACjB,GAAG;gBAAC6C,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,EAAG;gBAAAb,QAAA,eAClBhB,OAAA,CAAChB,IAAI,CAACwD,IAAI;kBACRH,IAAI,EAAC,MAAM;kBACXI,KAAK,EAAC,WAAW;kBACjBC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEpD,OAAO,EAAE;kBAAyB,CAAC,CAAE;kBAAAyB,QAAA,eAE/DhB,OAAA,CAACf,KAAK;oBAAC2D,IAAI,EAAC,OAAO;oBAACC,WAAW,EAAC;kBAAiB;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNvB,OAAA,CAACjB,GAAG;gBAAC6C,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,EAAG;gBAAAb,QAAA,eAClBhB,OAAA,CAAChB,IAAI,CAACwD,IAAI;kBACRH,IAAI,EAAC,OAAO;kBACZI,KAAK,EAAC,eAAe;kBACrBC,KAAK,EAAE,CACL;oBAAEC,QAAQ,EAAE,IAAI;oBAAEpD,OAAO,EAAE;kBAA0B,CAAC,EACtD;oBAAEuD,IAAI,EAAE,OAAO;oBAAEvD,OAAO,EAAE;kBAA6B,CAAC,CACxD;kBAAAyB,QAAA,eAEFhB,OAAA,CAACf,KAAK;oBAAC2D,IAAI,EAAC,OAAO;oBAACC,WAAW,EAAC;kBAAkB;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvB,OAAA,CAAClB,GAAG;cAAC6C,MAAM,EAAE,EAAG;cAAAX,QAAA,gBACdhB,OAAA,CAACjB,GAAG;gBAAC6C,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,EAAG;gBAAAb,QAAA,eAClBhB,OAAA,CAAChB,IAAI,CAACwD,IAAI;kBACRH,IAAI,EAAC,OAAO;kBACZI,KAAK,EAAC,cAAc;kBACpBC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEpD,OAAO,EAAE;kBAAiC,CAAC,CAAE;kBAAAyB,QAAA,eAEvEhB,OAAA,CAACf,KAAK;oBAAC2D,IAAI,EAAC,OAAO;oBAACC,WAAW,EAAC;kBAAyB;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNvB,OAAA,CAACjB,GAAG;gBAAC6C,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,EAAG;gBAAAb,QAAA,eAClBhB,OAAA,CAAChB,IAAI,CAACwD,IAAI;kBACRH,IAAI,EAAC,SAAS;kBACdI,KAAK,EAAC,SAAS;kBACfC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEpD,OAAO,EAAE;kBAA0B,CAAC,CAAE;kBAAAyB,QAAA,eAEhEhB,OAAA,CAACV,MAAM;oBAACsD,IAAI,EAAC,OAAO;oBAACC,WAAW,EAAC,kBAAkB;oBAAA7B,QAAA,gBACjDhB,OAAA,CAACI,MAAM;sBAAC2C,KAAK,EAAC,SAAS;sBAAA/B,QAAA,EAAC;oBAAe;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChDvB,OAAA,CAACI,MAAM;sBAAC2C,KAAK,EAAC,OAAO;sBAAA/B,QAAA,EAAC;oBAAe;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9CvB,OAAA,CAACI,MAAM;sBAAC2C,KAAK,EAAC,KAAK;sBAAA/B,QAAA,EAAC;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzCvB,OAAA,CAACI,MAAM;sBAAC2C,KAAK,EAAC,KAAK;sBAAA/B,QAAA,EAAC;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzCvB,OAAA,CAACI,MAAM;sBAAC2C,KAAK,EAAC,QAAQ;sBAAA/B,QAAA,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9CvB,OAAA,CAACI,MAAM;sBAAC2C,KAAK,EAAC,OAAO;sBAAA/B,QAAA,EAAC;oBAAK;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvB,OAAA,CAAChB,IAAI,CAACwD,IAAI;cACRH,IAAI,EAAC,SAAS;cACdI,KAAK,EAAC,SAAS;cACfC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpD,OAAO,EAAE;cAA4B,CAAC,CAAE;cAAAyB,QAAA,eAElEhB,OAAA,CAACG,QAAQ;gBACP6C,IAAI,EAAE,CAAE;gBACRH,WAAW,EAAC,uCAAuC;gBACnDD,IAAI,EAAC;cAAO;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZvB,OAAA,CAAChB,IAAI,CAACwD,IAAI;cAAAxB,QAAA,eACRhB,OAAA,CAACd,MAAM;gBACL4D,IAAI,EAAC,SAAS;gBACdG,QAAQ,EAAC,QAAQ;gBACjBL,IAAI,EAAC,OAAO;gBACZM,IAAI,eAAElD,OAAA,CAACJ,YAAY;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBR,SAAS,EAAC,oBAAoB;gBAC9BE,KAAK,EAAE;kBAAEkC,QAAQ,EAAE;gBAAI,CAAE;gBAAAnC,QAAA,EAC1B;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNvB,OAAA,CAACjB,GAAG;UAAC6C,EAAE,EAAE,EAAG;UAACQ,EAAE,EAAE,EAAG;UAAApB,QAAA,gBAClBhB,OAAA,CAACC,KAAK;YAAC+B,KAAK,EAAE,CAAE;YAAAhB,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrCvB,OAAA;YAAKiB,KAAK,EAAE;cAAEE,YAAY,EAAE;YAAG,CAAE;YAAAH,QAAA,eAC/BhB,OAAA;cACEoD,GAAG,EAAC,yBAAyB;cAC7BC,GAAG,EAAC,sBAAsB;cAC1BpC,KAAK,EAAE;gBAAEqC,KAAK,EAAE,MAAM;gBAAEC,YAAY,EAAE;cAAE;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNvB,OAAA,CAACb,IAAI;YAAA6B,QAAA,gBACHhB,OAAA,CAACC,KAAK;cAAC+B,KAAK,EAAE,CAAE;cAAAhB,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5CvB,OAAA,CAAClB,GAAG;cAAC6C,MAAM,EAAE,EAAG;cAACI,KAAK,EAAC,QAAQ;cAAAf,QAAA,gBAC7BhB,OAAA,CAACjB,GAAG;gBAAC6C,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,EAAG;gBAAAb,QAAA,gBAClBhB,OAAA,CAACE,SAAS;kBAACe,KAAK,EAAE;oBAAEE,YAAY,EAAE;kBAAE,CAAE;kBAAAH,QAAA,EAAC;gBAEvC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZvB,OAAA,CAACd,MAAM;kBACL4D,IAAI,EAAC,SAAS;kBACdI,IAAI,eAAElD,OAAA,CAACL,gBAAgB;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC3BN,KAAK,EAAE;oBAAEuC,SAAS,EAAE,EAAE;oBAAEC,UAAU,EAAE,SAAS;oBAAEC,WAAW,EAAE;kBAAU,CAAE;kBACxEC,IAAI,EAAE,iBAAiB9D,WAAW,CAAC+D,QAAQ,CAACC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAG;kBACrEC,MAAM,EAAC,QAAQ;kBAAA9C,QAAA,EAChB;gBAED;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNvB,OAAA,CAACjB,GAAG;gBAAC6C,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAACZ,KAAK,EAAE;kBAAEC,SAAS,EAAE;gBAAS,CAAE;gBAAAF,QAAA,eACjDhB,OAAA,CAACF,eAAe;kBACdiE,QAAQ,EAAC,qBAAqB;kBAC9BC,OAAO,EAAC,kBAAkB;kBAC1BC,WAAW,EAAC,EAAE;kBACdlD,SAAS,EAAC;gBAAkB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVvB,OAAA,CAACX,OAAO;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXvB,OAAA;MAASe,SAAS,EAAC,SAAS;MAAAC,QAAA,gBAC1BhB,OAAA,CAACC,KAAK;QAAC+B,KAAK,EAAE,CAAE;QAACf,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAH,QAAA,EAAC;MAA0B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrGvB,OAAA,CAAClB,GAAG;QAAC6C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACpBhB,OAAA,CAACjB,GAAG;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAb,QAAA,gBAClBhB,OAAA,CAACC,KAAK;YAAC+B,KAAK,EAAE,CAAE;YAAAhB,QAAA,EAAC;UAA8C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvEvB,OAAA,CAACE,SAAS;YAAAc,QAAA,EAAC;UAEX;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNvB,OAAA,CAACjB,GAAG;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAb,QAAA,gBAClBhB,OAAA,CAACC,KAAK;YAAC+B,KAAK,EAAE,CAAE;YAAAhB,QAAA,EAAC;UAA0B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDvB,OAAA,CAACE,SAAS;YAAAc,QAAA,EAAC;UAEX;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNvB,OAAA,CAACjB,GAAG;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAb,QAAA,gBAClBhB,OAAA,CAACC,KAAK;YAAC+B,KAAK,EAAE,CAAE;YAAAhB,QAAA,EAAC;UAAmC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5DvB,OAAA,CAACE,SAAS;YAAAc,QAAA,EAAC;UAEX;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNvB,OAAA,CAACjB,GAAG;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAb,QAAA,gBAClBhB,OAAA,CAACC,KAAK;YAAC+B,KAAK,EAAE,CAAE;YAAAhB,QAAA,EAAC;UAA2B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDvB,OAAA,CAACE,SAAS;YAAAc,QAAA,EAAC;UAEX;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvB,OAAA;MAASe,SAAS,EAAC,SAAS;MAACE,KAAK,EAAE;QAAEwC,UAAU,EAAE,SAAS;QAAES,OAAO,EAAE,EAAE;QAAEX,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEtC,SAAS,EAAE;MAAS,CAAE;MAAAF,QAAA,gBAC9HhB,OAAA,CAACC,KAAK;QAAC+B,KAAK,EAAE,CAAE;QAAAhB,QAAA,EAAC;MAA4B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrDvB,OAAA,CAACE,SAAS;QAACe,KAAK,EAAE;UAAEO,QAAQ,EAAE,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAc,CAAE;QAAAV,QAAA,EAAC;MAG1E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZvB,OAAA,CAACZ,KAAK;QAACwD,IAAI,EAAC,OAAO;QAAA5B,QAAA,gBACjBhB,OAAA,CAACd,MAAM;UAAC4D,IAAI,EAAC,SAAS;UAACF,IAAI,EAAC,OAAO;UAACM,IAAI,eAAElD,OAAA,CAACN,YAAY;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAP,QAAA,EAAC;QAE5D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvB,OAAA,CAACd,MAAM;UAAC0D,IAAI,EAAC,OAAO;UAACM,IAAI,eAAElD,OAAA,CAACP,aAAa;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAP,QAAA,EAAC;QAE9C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACjB,EAAA,CAlPID,WAAqB;EAAA,QACVrB,IAAI,CAACwB,OAAO;AAAA;AAAA2D,EAAA,GADvB9D,WAAqB;AAoP3B,eAAeA,WAAW;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}