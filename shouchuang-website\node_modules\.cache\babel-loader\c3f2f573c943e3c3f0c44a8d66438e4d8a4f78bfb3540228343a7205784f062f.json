{"ast": null, "code": "var _jsxFileName = \"C:\\\\customized\\\\ShouChuang Sporting\\\\\\u5EFA\\u7AD9\\u8D44\\u6599\\u6536\\u96C6\\uFF08\\u6700\\u65B0\\u7248\\uFF09\\\\shouchuang-website\\\\src\\\\pages\\\\Contact.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Typography, Row, Col, Form, Input, Button, Card, Space, Divider, Select, message } from 'antd';\nimport { EnvironmentOutlined, PhoneOutlined, MailOutlined, WhatsAppOutlined, SendOutlined } from '@ant-design/icons';\nimport { contactInfo } from '../models/mockData';\nimport WhatsAppContact from '../components/WhatsAppContact';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst ContactPage = () => {\n  _s();\n  const [form] = Form.useForm();\n  const onFinish = values => {\n    console.log('Form values:', values);\n    message.success('Your message has been sent successfully! We will contact you soon.');\n    form.resetFields();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: 48\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"Contact Us\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n        style: {\n          fontSize: 16,\n          maxWidth: 800,\n          margin: '0 auto'\n        },\n        children: \"Get in touch with our team to discuss your helmet manufacturing needs or request a quote for your project.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            style: {\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Space, {\n              align: \"center\",\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(EnvironmentOutlined, {\n                style: {\n                  fontSize: 24,\n                  color: '#0056b3'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                style: {\n                  margin: 0\n                },\n                children: \"Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                fontSize: 16\n              },\n              children: contactInfo.address\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            style: {\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Space, {\n              align: \"center\",\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(PhoneOutlined, {\n                style: {\n                  fontSize: 24,\n                  color: '#0056b3'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                style: {\n                  margin: 0\n                },\n                children: \"Phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                fontSize: 16\n              },\n              children: contactInfo.phone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            style: {\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Space, {\n              align: \"center\",\n              style: {\n                marginBottom: 16\n              },\n              children: [/*#__PURE__*/_jsxDEV(MailOutlined, {\n                style: {\n                  fontSize: 24,\n                  color: '#0056b3'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                style: {\n                  margin: 0\n                },\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                fontSize: 16\n              },\n              children: contactInfo.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [48, 48],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 14,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            children: \"Send Us a Message\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            style: {\n              marginBottom: 24\n            },\n            children: \"Fill out the form below to get in touch with our team. We'll respond to your inquiry as soon as possible.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            name: \"contact_form\",\n            layout: \"vertical\",\n            onFinish: onFinish,\n            requiredMark: false,\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"name\",\n                  label: \"Your Name\",\n                  rules: [{\n                    required: true,\n                    message: 'Please enter your name'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    size: \"large\",\n                    placeholder: \"Enter your name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 99,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"email\",\n                  label: \"Email Address\",\n                  rules: [{\n                    required: true,\n                    message: 'Please enter your email'\n                  }, {\n                    type: 'email',\n                    message: 'Please enter a valid email'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    size: \"large\",\n                    placeholder: \"Enter your email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 111,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"phone\",\n                  label: \"Phone Number\",\n                  rules: [{\n                    required: true,\n                    message: 'Please enter your phone number'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input, {\n                    size: \"large\",\n                    placeholder: \"Enter your phone number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"subject\",\n                  label: \"Subject\",\n                  rules: [{\n                    required: true,\n                    message: 'Please select a subject'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    size: \"large\",\n                    placeholder: \"Select a subject\",\n                    children: [/*#__PURE__*/_jsxDEV(Option, {\n                      value: \"general\",\n                      children: \"General Inquiry\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"quote\",\n                      children: \"Request a Quote\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"oem\",\n                      children: \"OEM Services\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"odm\",\n                      children: \"ODM Services\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 135,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"sample\",\n                      children: \"Sample Request\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Option, {\n                      value: \"other\",\n                      children: \"Other\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"message\",\n              label: \"Message\",\n              rules: [{\n                required: true,\n                message: 'Please enter your message'\n              }],\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 6,\n                placeholder: \"Enter your message or inquiry details\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                size: \"large\",\n                icon: /*#__PURE__*/_jsxDEV(SendOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 25\n                }, this),\n                style: {\n                  minWidth: 150\n                },\n                children: \"Send Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 10,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            children: \"Our Location\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 24\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/images/contact/map.jpg\",\n              alt: \"Company Location Map\",\n              style: {\n                width: '100%',\n                borderRadius: 8\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              children: \"Connect on WhatsApp\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              align: \"middle\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 16,\n                children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                  style: {\n                    marginBottom: 0\n                  },\n                  children: \"Scan the QR code or click the button below to connect with us on WhatsApp for quick responses to your inquiries.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  icon: /*#__PURE__*/_jsxDEV(WhatsAppOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 27\n                  }, this),\n                  style: {\n                    marginTop: 16,\n                    background: '#25D366',\n                    borderColor: '#25D366'\n                  },\n                  href: `https://wa.me/${contactInfo.whatsapp.replace(/[^0-9]/g, '')}`,\n                  target: \"_blank\",\n                  children: \"Chat on WhatsApp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                md: 8,\n                style: {\n                  textAlign: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(WhatsAppContact, {\n                  imageSrc: \"/images/whatApp.jpg\",\n                  altText: \"WhatsApp QR Code\",\n                  description: \"\",\n                  className: \"contact-whatsapp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          textAlign: 'center',\n          marginBottom: 32\n        },\n        children: \"Frequently Asked Questions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            children: \"What information should I provide for a quote?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            children: \"To provide an accurate quote, please include details such as the type of helmet, quantity, specific features, customization requirements, and target market (for certification purposes).\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            children: \"How can I request samples?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            children: \"You can request samples by contacting us through the form above or via email. Please specify the helmet model, quantity of samples needed, and any customization requirements.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            children: \"What is your typical response time?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            children: \"We typically respond to inquiries within 24-48 hours during business days. For urgent matters, we recommend contacting us via phone or WhatsApp.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            children: \"Do you offer factory tours?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            children: \"Yes, we welcome clients to visit our factory. Please contact us in advance to schedule a tour so we can make proper arrangements for your visit.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      style: {\n        background: '#f5f5f5',\n        padding: 32,\n        borderRadius: 8,\n        marginTop: 48,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        children: \"Ready to Start Your Project?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n        style: {\n          fontSize: 16,\n          maxWidth: 800,\n          margin: '0 auto 24px'\n        },\n        children: \"Contact us today to discuss your helmet manufacturing needs and discover how we can help bring your ideas to life. Our team is ready to provide you with the information and support you need to make your project a success.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        size: \"large\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"large\",\n          icon: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 53\n          }, this),\n          children: \"Email Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"large\",\n          icon: /*#__PURE__*/_jsxDEV(PhoneOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 38\n          }, this),\n          children: \"Call Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(ContactPage, \"rI7DrJIrFu7YmlGWYiMFTzs8jF0=\", false, function () {\n  return [Form.useForm];\n});\n_c = ContactPage;\nexport default ContactPage;\nvar _c;\n$RefreshReg$(_c, \"ContactPage\");", "map": {"version": 3, "names": ["React", "Typography", "Row", "Col", "Form", "Input", "<PERSON><PERSON>", "Card", "Space", "Divider", "Select", "message", "EnvironmentOutlined", "PhoneOutlined", "MailOutlined", "WhatsAppOutlined", "SendOutlined", "contactInfo", "WhatsAppContact", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "Text", "TextArea", "Option", "ContactPage", "_s", "form", "useForm", "onFinish", "values", "console", "log", "success", "resetFields", "className", "children", "style", "textAlign", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "max<PERSON><PERSON><PERSON>", "margin", "gutter", "xs", "md", "height", "align", "color", "level", "address", "phone", "email", "lg", "name", "layout", "requiredMark", "<PERSON><PERSON>", "label", "rules", "required", "size", "placeholder", "type", "value", "rows", "htmlType", "icon", "min<PERSON><PERSON><PERSON>", "src", "alt", "width", "borderRadius", "marginTop", "background", "borderColor", "href", "whatsapp", "replace", "target", "imageSrc", "altText", "description", "padding", "_c", "$RefreshReg$"], "sources": ["C:/customized/ShouChuang Sporting/建站资料收集（最新版）/shouchuang-website/src/pages/Contact.tsx"], "sourcesContent": ["import React from 'react';\nimport { Typography, Row, Col, Form, Input, Button, Card, Space, Divider, Select, message } from 'antd';\nimport {\n  EnvironmentOutlined,\n  PhoneOutlined,\n  MailOutlined,\n  WhatsAppOutlined,\n  SendOutlined\n} from '@ant-design/icons';\nimport { contactInfo } from '../models/mockData';\nimport WhatsAppContact from '../components/WhatsAppContact';\n\nconst { Title, Paragraph, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst ContactPage: React.FC = () => {\n  const [form] = Form.useForm();\n\n  const onFinish = (values: any) => {\n    console.log('Form values:', values);\n    message.success('Your message has been sent successfully! We will contact you soon.');\n    form.resetFields();\n  };\n\n  return (\n    <div className=\"page-container\">\n      {/* Page Header */}\n      <div style={{ textAlign: 'center', marginBottom: 48 }}>\n        <Title>Contact Us</Title>\n        <Paragraph style={{ fontSize: 16, maxWidth: 800, margin: '0 auto' }}>\n          Get in touch with our team to discuss your helmet manufacturing needs or request a quote for your project.\n        </Paragraph>\n      </div>\n\n      {/* Contact Information */}\n      <section className=\"section\">\n        <Row gutter={[24, 24]}>\n          <Col xs={24} md={8}>\n            <Card style={{ height: '100%' }}>\n              <Space align=\"center\" style={{ marginBottom: 16 }}>\n                <EnvironmentOutlined style={{ fontSize: 24, color: '#0056b3' }} />\n                <Title level={4} style={{ margin: 0 }}>Address</Title>\n              </Space>\n              <Paragraph style={{ fontSize: 16 }}>\n                {contactInfo.address}\n              </Paragraph>\n            </Card>\n          </Col>\n          <Col xs={24} md={8}>\n            <Card style={{ height: '100%' }}>\n              <Space align=\"center\" style={{ marginBottom: 16 }}>\n                <PhoneOutlined style={{ fontSize: 24, color: '#0056b3' }} />\n                <Title level={4} style={{ margin: 0 }}>Phone</Title>\n              </Space>\n              <Paragraph style={{ fontSize: 16 }}>\n                {contactInfo.phone}\n              </Paragraph>\n            </Card>\n          </Col>\n          <Col xs={24} md={8}>\n            <Card style={{ height: '100%' }}>\n              <Space align=\"center\" style={{ marginBottom: 16 }}>\n                <MailOutlined style={{ fontSize: 24, color: '#0056b3' }} />\n                <Title level={4} style={{ margin: 0 }}>Email</Title>\n              </Space>\n              <Paragraph style={{ fontSize: 16 }}>\n                {contactInfo.email}\n              </Paragraph>\n            </Card>\n          </Col>\n        </Row>\n      </section>\n\n      <Divider />\n\n      {/* Contact Form and Map */}\n      <section className=\"section\">\n        <Row gutter={[48, 48]}>\n          <Col xs={24} lg={14}>\n            <Title level={2}>Send Us a Message</Title>\n            <Paragraph style={{ marginBottom: 24 }}>\n              Fill out the form below to get in touch with our team. We'll respond to your inquiry as soon as possible.\n            </Paragraph>\n            <Form\n              form={form}\n              name=\"contact_form\"\n              layout=\"vertical\"\n              onFinish={onFinish}\n              requiredMark={false}\n            >\n              <Row gutter={16}>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name=\"name\"\n                    label=\"Your Name\"\n                    rules={[{ required: true, message: 'Please enter your name' }]}\n                  >\n                    <Input size=\"large\" placeholder=\"Enter your name\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name=\"email\"\n                    label=\"Email Address\"\n                    rules={[\n                      { required: true, message: 'Please enter your email' },\n                      { type: 'email', message: 'Please enter a valid email' }\n                    ]}\n                  >\n                    <Input size=\"large\" placeholder=\"Enter your email\" />\n                  </Form.Item>\n                </Col>\n              </Row>\n              <Row gutter={16}>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name=\"phone\"\n                    label=\"Phone Number\"\n                    rules={[{ required: true, message: 'Please enter your phone number' }]}\n                  >\n                    <Input size=\"large\" placeholder=\"Enter your phone number\" />\n                  </Form.Item>\n                </Col>\n                <Col xs={24} md={12}>\n                  <Form.Item\n                    name=\"subject\"\n                    label=\"Subject\"\n                    rules={[{ required: true, message: 'Please select a subject' }]}\n                  >\n                    <Select size=\"large\" placeholder=\"Select a subject\">\n                      <Option value=\"general\">General Inquiry</Option>\n                      <Option value=\"quote\">Request a Quote</Option>\n                      <Option value=\"oem\">OEM Services</Option>\n                      <Option value=\"odm\">ODM Services</Option>\n                      <Option value=\"sample\">Sample Request</Option>\n                      <Option value=\"other\">Other</Option>\n                    </Select>\n                  </Form.Item>\n                </Col>\n              </Row>\n              <Form.Item\n                name=\"message\"\n                label=\"Message\"\n                rules={[{ required: true, message: 'Please enter your message' }]}\n              >\n                <TextArea \n                  rows={6} \n                  placeholder=\"Enter your message or inquiry details\" \n                  size=\"large\" \n                />\n              </Form.Item>\n              <Form.Item>\n                <Button \n                  type=\"primary\" \n                  htmlType=\"submit\" \n                  size=\"large\" \n                  icon={<SendOutlined />}\n                  style={{ minWidth: 150 }}\n                >\n                  Send Message\n                </Button>\n              </Form.Item>\n            </Form>\n          </Col>\n          <Col xs={24} lg={10}>\n            <Title level={2}>Our Location</Title>\n            <div style={{ marginBottom: 24 }}>\n              <img \n                src=\"/images/contact/map.jpg\" \n                alt=\"Company Location Map\" \n                style={{ width: '100%', borderRadius: 8 }} \n              />\n            </div>\n            <Card>\n              <Title level={4}>Connect on WhatsApp</Title>\n              <Row gutter={16} align=\"middle\">\n                <Col xs={24} md={16}>\n                  <Paragraph style={{ marginBottom: 0 }}>\n                    Scan the QR code or click the button below to connect with us on WhatsApp for quick responses to your inquiries.\n                  </Paragraph>\n                  <Button\n                    type=\"primary\"\n                    icon={<WhatsAppOutlined />}\n                    style={{ marginTop: 16, background: '#25D366', borderColor: '#25D366' }}\n                    href={`https://wa.me/${contactInfo.whatsapp.replace(/[^0-9]/g, '')}`}\n                    target=\"_blank\"\n                  >\n                    Chat on WhatsApp\n                  </Button>\n                </Col>\n                <Col xs={24} md={8} style={{ textAlign: 'center' }}>\n                  <WhatsAppContact\n                    imageSrc=\"/images/whatApp.jpg\"\n                    altText=\"WhatsApp QR Code\"\n                    description=\"\"\n                    className=\"contact-whatsapp\"\n                  />\n                </Col>\n              </Row>\n            </Card>\n          </Col>\n        </Row>\n      </section>\n\n      <Divider />\n\n      {/* FAQ Section */}\n      <section className=\"section\">\n        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>Frequently Asked Questions</Title>\n        <Row gutter={[24, 24]}>\n          <Col xs={24} md={12}>\n            <Title level={4}>What information should I provide for a quote?</Title>\n            <Paragraph>\n              To provide an accurate quote, please include details such as the type of helmet, quantity, specific features, customization requirements, and target market (for certification purposes).\n            </Paragraph>\n          </Col>\n          <Col xs={24} md={12}>\n            <Title level={4}>How can I request samples?</Title>\n            <Paragraph>\n              You can request samples by contacting us through the form above or via email. Please specify the helmet model, quantity of samples needed, and any customization requirements.\n            </Paragraph>\n          </Col>\n          <Col xs={24} md={12}>\n            <Title level={4}>What is your typical response time?</Title>\n            <Paragraph>\n              We typically respond to inquiries within 24-48 hours during business days. For urgent matters, we recommend contacting us via phone or WhatsApp.\n            </Paragraph>\n          </Col>\n          <Col xs={24} md={12}>\n            <Title level={4}>Do you offer factory tours?</Title>\n            <Paragraph>\n              Yes, we welcome clients to visit our factory. Please contact us in advance to schedule a tour so we can make proper arrangements for your visit.\n            </Paragraph>\n          </Col>\n        </Row>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"section\" style={{ background: '#f5f5f5', padding: 32, borderRadius: 8, marginTop: 48, textAlign: 'center' }}>\n        <Title level={2}>Ready to Start Your Project?</Title>\n        <Paragraph style={{ fontSize: 16, maxWidth: 800, margin: '0 auto 24px' }}>\n          Contact us today to discuss your helmet manufacturing needs and discover how we can help bring your ideas to life.\n          Our team is ready to provide you with the information and support you need to make your project a success.\n        </Paragraph>\n        <Space size=\"large\">\n          <Button type=\"primary\" size=\"large\" icon={<MailOutlined />}>\n            Email Us\n          </Button>\n          <Button size=\"large\" icon={<PhoneOutlined />}>\n            Call Us\n          </Button>\n        </Space>\n      </section>\n    </div>\n  );\n};\n\nexport default ContactPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACvG,SACEC,mBAAmB,EACnBC,aAAa,EACbC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,QACP,mBAAmB;AAC1B,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,eAAe,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAM;EAAEC,KAAK;EAAEC,SAAS;EAAEC;AAAK,CAAC,GAAGtB,UAAU;AAC7C,MAAM;EAAEuB;AAAS,CAAC,GAAGnB,KAAK;AAC1B,MAAM;EAAEoB;AAAO,CAAC,GAAGf,MAAM;AAEzB,MAAMgB,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,IAAI,CAAC,GAAGxB,IAAI,CAACyB,OAAO,CAAC,CAAC;EAE7B,MAAMC,QAAQ,GAAIC,MAAW,IAAK;IAChCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,MAAM,CAAC;IACnCpB,OAAO,CAACuB,OAAO,CAAC,oEAAoE,CAAC;IACrFN,IAAI,CAACO,WAAW,CAAC,CAAC;EACpB,CAAC;EAED,oBACEf,OAAA;IAAKgB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAE7BjB,OAAA;MAAKkB,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBACpDjB,OAAA,CAACC,KAAK;QAAAgB,QAAA,EAAC;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACzBxB,OAAA,CAACE,SAAS;QAACgB,KAAK,EAAE;UAAEO,QAAQ,EAAE,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAV,QAAA,EAAC;MAErE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGNxB,OAAA;MAASgB,SAAS,EAAC,SAAS;MAAAC,QAAA,eAC1BjB,OAAA,CAAClB,GAAG;QAAC8C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACpBjB,OAAA,CAACjB,GAAG;UAAC8C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,eACjBjB,OAAA,CAACb,IAAI;YAAC+B,KAAK,EAAE;cAAEa,MAAM,EAAE;YAAO,CAAE;YAAAd,QAAA,gBAC9BjB,OAAA,CAACZ,KAAK;cAAC4C,KAAK,EAAC,QAAQ;cAACd,KAAK,EAAE;gBAAEE,YAAY,EAAE;cAAG,CAAE;cAAAH,QAAA,gBAChDjB,OAAA,CAACR,mBAAmB;gBAAC0B,KAAK,EAAE;kBAAEO,QAAQ,EAAE,EAAE;kBAAEQ,KAAK,EAAE;gBAAU;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClExB,OAAA,CAACC,KAAK;gBAACiC,KAAK,EAAE,CAAE;gBAAChB,KAAK,EAAE;kBAAES,MAAM,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EAAC;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACRxB,OAAA,CAACE,SAAS;cAACgB,KAAK,EAAE;gBAAEO,QAAQ,EAAE;cAAG,CAAE;cAAAR,QAAA,EAChCpB,WAAW,CAACsC;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNxB,OAAA,CAACjB,GAAG;UAAC8C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,eACjBjB,OAAA,CAACb,IAAI;YAAC+B,KAAK,EAAE;cAAEa,MAAM,EAAE;YAAO,CAAE;YAAAd,QAAA,gBAC9BjB,OAAA,CAACZ,KAAK;cAAC4C,KAAK,EAAC,QAAQ;cAACd,KAAK,EAAE;gBAAEE,YAAY,EAAE;cAAG,CAAE;cAAAH,QAAA,gBAChDjB,OAAA,CAACP,aAAa;gBAACyB,KAAK,EAAE;kBAAEO,QAAQ,EAAE,EAAE;kBAAEQ,KAAK,EAAE;gBAAU;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5DxB,OAAA,CAACC,KAAK;gBAACiC,KAAK,EAAE,CAAE;gBAAChB,KAAK,EAAE;kBAAES,MAAM,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACRxB,OAAA,CAACE,SAAS;cAACgB,KAAK,EAAE;gBAAEO,QAAQ,EAAE;cAAG,CAAE;cAAAR,QAAA,EAChCpB,WAAW,CAACuC;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNxB,OAAA,CAACjB,GAAG;UAAC8C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,eACjBjB,OAAA,CAACb,IAAI;YAAC+B,KAAK,EAAE;cAAEa,MAAM,EAAE;YAAO,CAAE;YAAAd,QAAA,gBAC9BjB,OAAA,CAACZ,KAAK;cAAC4C,KAAK,EAAC,QAAQ;cAACd,KAAK,EAAE;gBAAEE,YAAY,EAAE;cAAG,CAAE;cAAAH,QAAA,gBAChDjB,OAAA,CAACN,YAAY;gBAACwB,KAAK,EAAE;kBAAEO,QAAQ,EAAE,EAAE;kBAAEQ,KAAK,EAAE;gBAAU;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3DxB,OAAA,CAACC,KAAK;gBAACiC,KAAK,EAAE,CAAE;gBAAChB,KAAK,EAAE;kBAAES,MAAM,EAAE;gBAAE,CAAE;gBAAAV,QAAA,EAAC;cAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACRxB,OAAA,CAACE,SAAS;cAACgB,KAAK,EAAE;gBAAEO,QAAQ,EAAE;cAAG,CAAE;cAAAR,QAAA,EAChCpB,WAAW,CAACwC;YAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVxB,OAAA,CAACX,OAAO;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXxB,OAAA;MAASgB,SAAS,EAAC,SAAS;MAAAC,QAAA,eAC1BjB,OAAA,CAAClB,GAAG;QAAC8C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACpBjB,OAAA,CAACjB,GAAG;UAAC8C,EAAE,EAAE,EAAG;UAACS,EAAE,EAAE,EAAG;UAAArB,QAAA,gBAClBjB,OAAA,CAACC,KAAK;YAACiC,KAAK,EAAE,CAAE;YAAAjB,QAAA,EAAC;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1CxB,OAAA,CAACE,SAAS;YAACgB,KAAK,EAAE;cAAEE,YAAY,EAAE;YAAG,CAAE;YAAAH,QAAA,EAAC;UAExC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZxB,OAAA,CAAChB,IAAI;YACHwB,IAAI,EAAEA,IAAK;YACX+B,IAAI,EAAC,cAAc;YACnBC,MAAM,EAAC,UAAU;YACjB9B,QAAQ,EAAEA,QAAS;YACnB+B,YAAY,EAAE,KAAM;YAAAxB,QAAA,gBAEpBjB,OAAA,CAAClB,GAAG;cAAC8C,MAAM,EAAE,EAAG;cAAAX,QAAA,gBACdjB,OAAA,CAACjB,GAAG;gBAAC8C,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,EAAG;gBAAAb,QAAA,eAClBjB,OAAA,CAAChB,IAAI,CAAC0D,IAAI;kBACRH,IAAI,EAAC,MAAM;kBACXI,KAAK,EAAC,WAAW;kBACjBC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEtD,OAAO,EAAE;kBAAyB,CAAC,CAAE;kBAAA0B,QAAA,eAE/DjB,OAAA,CAACf,KAAK;oBAAC6D,IAAI,EAAC,OAAO;oBAACC,WAAW,EAAC;kBAAiB;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxB,OAAA,CAACjB,GAAG;gBAAC8C,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,EAAG;gBAAAb,QAAA,eAClBjB,OAAA,CAAChB,IAAI,CAAC0D,IAAI;kBACRH,IAAI,EAAC,OAAO;kBACZI,KAAK,EAAC,eAAe;kBACrBC,KAAK,EAAE,CACL;oBAAEC,QAAQ,EAAE,IAAI;oBAAEtD,OAAO,EAAE;kBAA0B,CAAC,EACtD;oBAAEyD,IAAI,EAAE,OAAO;oBAAEzD,OAAO,EAAE;kBAA6B,CAAC,CACxD;kBAAA0B,QAAA,eAEFjB,OAAA,CAACf,KAAK;oBAAC6D,IAAI,EAAC,OAAO;oBAACC,WAAW,EAAC;kBAAkB;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxB,OAAA,CAAClB,GAAG;cAAC8C,MAAM,EAAE,EAAG;cAAAX,QAAA,gBACdjB,OAAA,CAACjB,GAAG;gBAAC8C,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,EAAG;gBAAAb,QAAA,eAClBjB,OAAA,CAAChB,IAAI,CAAC0D,IAAI;kBACRH,IAAI,EAAC,OAAO;kBACZI,KAAK,EAAC,cAAc;kBACpBC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEtD,OAAO,EAAE;kBAAiC,CAAC,CAAE;kBAAA0B,QAAA,eAEvEjB,OAAA,CAACf,KAAK;oBAAC6D,IAAI,EAAC,OAAO;oBAACC,WAAW,EAAC;kBAAyB;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxB,OAAA,CAACjB,GAAG;gBAAC8C,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,EAAG;gBAAAb,QAAA,eAClBjB,OAAA,CAAChB,IAAI,CAAC0D,IAAI;kBACRH,IAAI,EAAC,SAAS;kBACdI,KAAK,EAAC,SAAS;kBACfC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEtD,OAAO,EAAE;kBAA0B,CAAC,CAAE;kBAAA0B,QAAA,eAEhEjB,OAAA,CAACV,MAAM;oBAACwD,IAAI,EAAC,OAAO;oBAACC,WAAW,EAAC,kBAAkB;oBAAA9B,QAAA,gBACjDjB,OAAA,CAACK,MAAM;sBAAC4C,KAAK,EAAC,SAAS;sBAAAhC,QAAA,EAAC;oBAAe;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChDxB,OAAA,CAACK,MAAM;sBAAC4C,KAAK,EAAC,OAAO;sBAAAhC,QAAA,EAAC;oBAAe;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9CxB,OAAA,CAACK,MAAM;sBAAC4C,KAAK,EAAC,KAAK;sBAAAhC,QAAA,EAAC;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzCxB,OAAA,CAACK,MAAM;sBAAC4C,KAAK,EAAC,KAAK;sBAAAhC,QAAA,EAAC;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzCxB,OAAA,CAACK,MAAM;sBAAC4C,KAAK,EAAC,QAAQ;sBAAAhC,QAAA,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9CxB,OAAA,CAACK,MAAM;sBAAC4C,KAAK,EAAC,OAAO;sBAAAhC,QAAA,EAAC;oBAAK;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxB,OAAA,CAAChB,IAAI,CAAC0D,IAAI;cACRH,IAAI,EAAC,SAAS;cACdI,KAAK,EAAC,SAAS;cACfC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtD,OAAO,EAAE;cAA4B,CAAC,CAAE;cAAA0B,QAAA,eAElEjB,OAAA,CAACI,QAAQ;gBACP8C,IAAI,EAAE,CAAE;gBACRH,WAAW,EAAC,uCAAuC;gBACnDD,IAAI,EAAC;cAAO;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZxB,OAAA,CAAChB,IAAI,CAAC0D,IAAI;cAAAzB,QAAA,eACRjB,OAAA,CAACd,MAAM;gBACL8D,IAAI,EAAC,SAAS;gBACdG,QAAQ,EAAC,QAAQ;gBACjBL,IAAI,EAAC,OAAO;gBACZM,IAAI,eAAEpD,OAAA,CAACJ,YAAY;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBN,KAAK,EAAE;kBAAEmC,QAAQ,EAAE;gBAAI,CAAE;gBAAApC,QAAA,EAC1B;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNxB,OAAA,CAACjB,GAAG;UAAC8C,EAAE,EAAE,EAAG;UAACS,EAAE,EAAE,EAAG;UAAArB,QAAA,gBAClBjB,OAAA,CAACC,KAAK;YAACiC,KAAK,EAAE,CAAE;YAAAjB,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrCxB,OAAA;YAAKkB,KAAK,EAAE;cAAEE,YAAY,EAAE;YAAG,CAAE;YAAAH,QAAA,eAC/BjB,OAAA;cACEsD,GAAG,EAAC,yBAAyB;cAC7BC,GAAG,EAAC,sBAAsB;cAC1BrC,KAAK,EAAE;gBAAEsC,KAAK,EAAE,MAAM;gBAAEC,YAAY,EAAE;cAAE;YAAE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNxB,OAAA,CAACb,IAAI;YAAA8B,QAAA,gBACHjB,OAAA,CAACC,KAAK;cAACiC,KAAK,EAAE,CAAE;cAAAjB,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5CxB,OAAA,CAAClB,GAAG;cAAC8C,MAAM,EAAE,EAAG;cAACI,KAAK,EAAC,QAAQ;cAAAf,QAAA,gBAC7BjB,OAAA,CAACjB,GAAG;gBAAC8C,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,EAAG;gBAAAb,QAAA,gBAClBjB,OAAA,CAACE,SAAS;kBAACgB,KAAK,EAAE;oBAAEE,YAAY,EAAE;kBAAE,CAAE;kBAAAH,QAAA,EAAC;gBAEvC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZxB,OAAA,CAACd,MAAM;kBACL8D,IAAI,EAAC,SAAS;kBACdI,IAAI,eAAEpD,OAAA,CAACL,gBAAgB;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC3BN,KAAK,EAAE;oBAAEwC,SAAS,EAAE,EAAE;oBAAEC,UAAU,EAAE,SAAS;oBAAEC,WAAW,EAAE;kBAAU,CAAE;kBACxEC,IAAI,EAAE,iBAAiBhE,WAAW,CAACiE,QAAQ,CAACC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAG;kBACrEC,MAAM,EAAC,QAAQ;kBAAA/C,QAAA,EAChB;gBAED;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNxB,OAAA,CAACjB,GAAG;gBAAC8C,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAACZ,KAAK,EAAE;kBAAEC,SAAS,EAAE;gBAAS,CAAE;gBAAAF,QAAA,eACjDjB,OAAA,CAACF,eAAe;kBACdmE,QAAQ,EAAC,qBAAqB;kBAC9BC,OAAO,EAAC,kBAAkB;kBAC1BC,WAAW,EAAC,EAAE;kBACdnD,SAAS,EAAC;gBAAkB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVxB,OAAA,CAACX,OAAO;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXxB,OAAA;MAASgB,SAAS,EAAC,SAAS;MAAAC,QAAA,gBAC1BjB,OAAA,CAACC,KAAK;QAACiC,KAAK,EAAE,CAAE;QAAChB,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAH,QAAA,EAAC;MAA0B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrGxB,OAAA,CAAClB,GAAG;QAAC8C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACpBjB,OAAA,CAACjB,GAAG;UAAC8C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAb,QAAA,gBAClBjB,OAAA,CAACC,KAAK;YAACiC,KAAK,EAAE,CAAE;YAAAjB,QAAA,EAAC;UAA8C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvExB,OAAA,CAACE,SAAS;YAAAe,QAAA,EAAC;UAEX;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNxB,OAAA,CAACjB,GAAG;UAAC8C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAb,QAAA,gBAClBjB,OAAA,CAACC,KAAK;YAACiC,KAAK,EAAE,CAAE;YAAAjB,QAAA,EAAC;UAA0B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDxB,OAAA,CAACE,SAAS;YAAAe,QAAA,EAAC;UAEX;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNxB,OAAA,CAACjB,GAAG;UAAC8C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAb,QAAA,gBAClBjB,OAAA,CAACC,KAAK;YAACiC,KAAK,EAAE,CAAE;YAAAjB,QAAA,EAAC;UAAmC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5DxB,OAAA,CAACE,SAAS;YAAAe,QAAA,EAAC;UAEX;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNxB,OAAA,CAACjB,GAAG;UAAC8C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAb,QAAA,gBAClBjB,OAAA,CAACC,KAAK;YAACiC,KAAK,EAAE,CAAE;YAAAjB,QAAA,EAAC;UAA2B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpDxB,OAAA,CAACE,SAAS;YAAAe,QAAA,EAAC;UAEX;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVxB,OAAA;MAASgB,SAAS,EAAC,SAAS;MAACE,KAAK,EAAE;QAAEyC,UAAU,EAAE,SAAS;QAAES,OAAO,EAAE,EAAE;QAAEX,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEvC,SAAS,EAAE;MAAS,CAAE;MAAAF,QAAA,gBAC9HjB,OAAA,CAACC,KAAK;QAACiC,KAAK,EAAE,CAAE;QAAAjB,QAAA,EAAC;MAA4B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACrDxB,OAAA,CAACE,SAAS;QAACgB,KAAK,EAAE;UAAEO,QAAQ,EAAE,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAc,CAAE;QAAAV,QAAA,EAAC;MAG1E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZxB,OAAA,CAACZ,KAAK;QAAC0D,IAAI,EAAC,OAAO;QAAA7B,QAAA,gBACjBjB,OAAA,CAACd,MAAM;UAAC8D,IAAI,EAAC,SAAS;UAACF,IAAI,EAAC,OAAO;UAACM,IAAI,eAAEpD,OAAA,CAACN,YAAY;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAP,QAAA,EAAC;QAE5D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxB,OAAA,CAACd,MAAM;UAAC4D,IAAI,EAAC,OAAO;UAACM,IAAI,eAAEpD,OAAA,CAACP,aAAa;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAP,QAAA,EAAC;QAE9C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACjB,EAAA,CAhPID,WAAqB;EAAA,QACVtB,IAAI,CAACyB,OAAO;AAAA;AAAA4D,EAAA,GADvB/D,WAAqB;AAkP3B,eAAeA,WAAW;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}