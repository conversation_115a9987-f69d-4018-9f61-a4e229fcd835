{"ast": null, "code": "export const products = [{\n  id: 'road-cycling',\n  name: 'Road Cycling Helmet',\n  description: 'Aerodynamic streamlined shape, wind tunnel test reduces wind resistance by 15%, helping to break through the speed. 22 three-dimensional ventilation ducts + internal guide grooves continuously take away heat and keep the head dry.',\n  features: ['Aerodynamic streamlined shape', 'Wind tunnel tested - reduces wind resistance by 15%', '22 three-dimensional ventilation ducts', 'Internal guide grooves for heat dissipation', 'Carbon fiber composite structure weighs only 240g', '360° adjustment system, fits Asian head shape', 'UV protective coating lenses (optional)'],\n  image: '/images/products/road-cycling.jpg',\n  category: 'Cycling'\n}, {\n  id: 'mountain-bike',\n  name: 'Mountain Bike Helmet',\n  description: 'The ultimate choice for all-terrain riders! Ultra-lightweight carbon fiber shell with honeycomb ventilation holes, high strength impact resistance and efficient heat dissipation.',\n  features: ['Ultra-lightweight carbon fiber shell', 'Honeycomb ventilation holes', 'High strength impact resistance', 'Efficient heat dissipation', 'Rear extension design protects the back of the head', 'Compatible with sports cameras and headlight brackets', 'Detachable insect repellent mask', 'EN 1078 safety certification', 'Quick-release magnetic buckle'],\n  image: '/images/products/mountain-bike.jpg',\n  category: 'Cycling'\n}, {\n  id: 'kids-helmet',\n  name: 'Children\\'s Helmet',\n  description: 'Specially developed for children aged 3-12, the food-grade silicone lining is soft and skin-friendly, and the detachable and washable design solves the hygiene pain point.',\n  features: ['Designed for children aged 3-12', 'Food-grade silicone lining - soft and skin-friendly', 'Detachable and washable design', 'Cartoon IP co-branded appearance', 'Back-head knob-type head circumference adjustment system', 'Double-layer shell structure (ABS shell + high-density foam)', 'Side reflective strips for nighttime visibility', 'Electronic fence setup through APP (optional)'],\n  image: '/images/products/kids-helmet.jpg',\n  category: 'Children'\n}, {\n  id: 'smart-helmet',\n  name: 'Smart Bicycle Helmet',\n  description: 'Designed for urban commuters and technology enthusiasts, it integrates cutting-edge intelligent technology: built-in LED warning light, Bluetooth connection, and collision sensing system.',\n  features: ['Built-in LED warning light with automatic light sensitivity adjustment', 'Bluetooth connection to mobile phones', 'Real-time navigation and call reminders through APP', 'Collision sensing system with automatic distress signal', 'High-impact PC material outer shell', 'Inner EPS buffer layer + MIPS anti-concussion technology'],\n  image: '/images/products/smart-helmet.jpg',\n  category: 'Smart'\n}, {\n  id: 'skateboard-helmet',\n  name: 'Skateboard Helmet',\n  description: 'Lightweight yet protective helmet for skateboarding, rollerblading, and action sports.',\n  features: ['ABS shell + PC inner layer, weighing only 350-400g', 'Extended rear and ear protection', 'Multi-directional airflow channels', 'Sweat-absorbing inner padding', 'Stylish colors + reflective strips for nighttime safety'],\n  image: '/images/products/skateboard-helmet.jpg',\n  category: 'Action Sports'\n}, {\n  id: 'snow-helmet',\n  name: 'Snow Helmet',\n  description: 'Cold-weather helmet for skiing/snowboarding with integrated warmth and fog prevention.',\n  features: ['Removable fleece liner compatible with goggles', 'Hard ABS shell + soft inner layer for multi-impact protection', 'Anti-fog ventilation + goggle clip compatibility', 'Built-in speaker slots without compromising insulation'],\n  image: '/images/products/snow-helmet.jpg',\n  category: 'Winter Sports'\n}, {\n  id: 'motorcycle-helmet',\n  name: 'Motorcycle Helmet',\n  description: 'Full-coverage helmet for road riders, optimized for high-speed safety and noise reduction.',\n  features: ['Carbon fiber or composite fiber shell for lightweight durability', 'Dual-density EPS liner + antibacterial removable padding', 'Eyewear-compatible design', 'Anti-fog/UV-resistant lens with quick-release replacement system', 'DOT/ECE 22.06 certified', 'Bluetooth headset compatible'],\n  image: '/images/products/motorcycle-helmet.jpg',\n  category: 'Motorcycle'\n}, {\n  id: 'horse-riding-helmet',\n  name: 'Equestrian Helmet',\n  description: 'Professional helmet for equestrian sports, offering head/neck protection during falls.',\n  features: ['MIPS (Multi-directional Impact Protection System)', '3D contoured removable/washable liner', 'Dial-adjustable headband', 'Extended rear coverage to protect the base of the skull and neck', 'SEI-certified (International Equestrian Safety Standard)'],\n  image: '/images/products/horse-riding-helmet.jpg',\n  category: 'Equestrian'\n}];\nexport const services = [{\n  id: 'oem',\n  title: 'OEM Manufacturing',\n  description: 'We provide full Original Equipment Manufacturing services, producing helmets according to your exact specifications and branding them with your logo and design elements.',\n  icon: 'factory'\n}, {\n  id: 'odm',\n  title: 'ODM Solutions',\n  description: 'Our Original Design Manufacturing service offers ready-made helmet designs that can be customized with your branding, saving you time and development costs.',\n  icon: 'tool'\n}, {\n  id: 'design',\n  title: 'Custom Design',\n  description: 'Our experienced design team works closely with you to create unique helmet designs that meet your specific requirements and stand out in the market.',\n  icon: 'design'\n}, {\n  id: 'rd',\n  title: 'R&D Capabilities',\n  description: 'We invest heavily in research and development to create innovative helmet technologies and designs that improve safety, comfort, and performance.',\n  icon: 'experiment'\n}, {\n  id: 'quality',\n  title: 'Quality Assurance',\n  description: 'Our rigorous quality control processes ensure that all helmets meet or exceed international safety standards and your specific requirements.',\n  icon: 'safety'\n}, {\n  id: 'logistics',\n  title: 'Global Logistics',\n  description: 'We handle all aspects of shipping and logistics, ensuring your products are delivered on time and in perfect condition anywhere in the world.',\n  icon: 'global'\n}];\nexport const factoryFeatures = [{\n  id: 'manufacturing',\n  title: 'State-of-the-Art Manufacturing',\n  description: 'Our modern manufacturing facility is equipped with advanced production lines and quality control systems to ensure the highest standards for all our products.',\n  image: '/images/factory/manufacturing.jpg'\n}, {\n  id: 'rd-lab',\n  title: 'R&D Capabilities',\n  description: 'Our dedicated research and development team continuously works on new designs and technologies to improve helmet safety, comfort, and performance.',\n  image: '/images/factory/rd-lab.jpg'\n}, {\n  id: 'quality-control',\n  title: 'Quality Control',\n  description: 'We implement strict quality control procedures at every stage of production to ensure all helmets meet international safety standards and customer specifications.',\n  image: '/images/factory/quality-control.jpg'\n}, {\n  id: 'customization',\n  title: 'Customization Workshop',\n  description: 'Our specialized customization workshop handles all aspects of helmet personalization, from graphics and colors to special features and accessories.',\n  image: '/images/factory/customization.jpg'\n}];\nexport const contactInfo = {\n  address: 'Building B, South Securities Building, 1916B, Jianshe Road, Jiabei Community, Nanhu Street, Luohu District, Shenzhen, China',\n  phone: '+8613509806025',\n  email: '<EMAIL>',\n  whatsapp: '+8613509806025'\n};", "map": {"version": 3, "names": ["products", "id", "name", "description", "features", "image", "category", "services", "title", "icon", "factoryFeatures", "contactInfo", "address", "phone", "email", "whatsapp"], "sources": ["C:/customized/ShouChuang Sporting/建站资料收集（最新版）/shouchuang-website/src/models/mockData.ts"], "sourcesContent": ["import { Product, Service, FactoryFeature, ContactInfo } from './types';\n\nexport const products: Product[] = [\n  {\n    id: 'road-cycling',\n    name: 'Road Cycling Helmet',\n    description: 'Aerodynamic streamlined shape, wind tunnel test reduces wind resistance by 15%, helping to break through the speed. 22 three-dimensional ventilation ducts + internal guide grooves continuously take away heat and keep the head dry.',\n    features: [\n      'Aerodynamic streamlined shape',\n      'Wind tunnel tested - reduces wind resistance by 15%',\n      '22 three-dimensional ventilation ducts',\n      'Internal guide grooves for heat dissipation',\n      'Carbon fiber composite structure weighs only 240g',\n      '360° adjustment system, fits Asian head shape',\n      'UV protective coating lenses (optional)'\n    ],\n    image: '/images/products/road-cycling.jpg',\n    category: 'Cycling'\n  },\n  {\n    id: 'mountain-bike',\n    name: 'Mountain Bike Helmet',\n    description: 'The ultimate choice for all-terrain riders! Ultra-lightweight carbon fiber shell with honeycomb ventilation holes, high strength impact resistance and efficient heat dissipation.',\n    features: [\n      'Ultra-lightweight carbon fiber shell',\n      'Honeycomb ventilation holes',\n      'High strength impact resistance',\n      'Efficient heat dissipation',\n      'Rear extension design protects the back of the head',\n      'Compatible with sports cameras and headlight brackets',\n      'Detachable insect repellent mask',\n      'EN 1078 safety certification',\n      'Quick-release magnetic buckle'\n    ],\n    image: '/images/products/mountain-bike.jpg',\n    category: 'Cycling'\n  },\n  {\n    id: 'kids-helmet',\n    name: 'Children\\'s Helmet',\n    description: 'Specially developed for children aged 3-12, the food-grade silicone lining is soft and skin-friendly, and the detachable and washable design solves the hygiene pain point.',\n    features: [\n      'Designed for children aged 3-12',\n      'Food-grade silicone lining - soft and skin-friendly',\n      'Detachable and washable design',\n      'Cartoon IP co-branded appearance',\n      'Back-head knob-type head circumference adjustment system',\n      'Double-layer shell structure (ABS shell + high-density foam)',\n      'Side reflective strips for nighttime visibility',\n      'Electronic fence setup through APP (optional)'\n    ],\n    image: '/images/products/kids-helmet.jpg',\n    category: 'Children'\n  },\n  {\n    id: 'smart-helmet',\n    name: 'Smart Bicycle Helmet',\n    description: 'Designed for urban commuters and technology enthusiasts, it integrates cutting-edge intelligent technology: built-in LED warning light, Bluetooth connection, and collision sensing system.',\n    features: [\n      'Built-in LED warning light with automatic light sensitivity adjustment',\n      'Bluetooth connection to mobile phones',\n      'Real-time navigation and call reminders through APP',\n      'Collision sensing system with automatic distress signal',\n      'High-impact PC material outer shell',\n      'Inner EPS buffer layer + MIPS anti-concussion technology'\n    ],\n    image: '/images/products/smart-helmet.jpg',\n    category: 'Smart'\n  },\n  {\n    id: 'skateboard-helmet',\n    name: 'Skateboard Helmet',\n    description: 'Lightweight yet protective helmet for skateboarding, rollerblading, and action sports.',\n    features: [\n      'ABS shell + PC inner layer, weighing only 350-400g',\n      'Extended rear and ear protection',\n      'Multi-directional airflow channels',\n      'Sweat-absorbing inner padding',\n      'Stylish colors + reflective strips for nighttime safety'\n    ],\n    image: '/images/products/skateboard-helmet.jpg',\n    category: 'Action Sports'\n  },\n  {\n    id: 'snow-helmet',\n    name: 'Snow Helmet',\n    description: 'Cold-weather helmet for skiing/snowboarding with integrated warmth and fog prevention.',\n    features: [\n      'Removable fleece liner compatible with goggles',\n      'Hard ABS shell + soft inner layer for multi-impact protection',\n      'Anti-fog ventilation + goggle clip compatibility',\n      'Built-in speaker slots without compromising insulation'\n    ],\n    image: '/images/products/snow-helmet.jpg',\n    category: 'Winter Sports'\n  },\n  {\n    id: 'motorcycle-helmet',\n    name: 'Motorcycle Helmet',\n    description: 'Full-coverage helmet for road riders, optimized for high-speed safety and noise reduction.',\n    features: [\n      'Carbon fiber or composite fiber shell for lightweight durability',\n      'Dual-density EPS liner + antibacterial removable padding',\n      'Eyewear-compatible design',\n      'Anti-fog/UV-resistant lens with quick-release replacement system',\n      'DOT/ECE 22.06 certified',\n      'Bluetooth headset compatible'\n    ],\n    image: '/images/products/motorcycle-helmet.jpg',\n    category: 'Motorcycle'\n  },\n  {\n    id: 'horse-riding-helmet',\n    name: 'Equestrian Helmet',\n    description: 'Professional helmet for equestrian sports, offering head/neck protection during falls.',\n    features: [\n      'MIPS (Multi-directional Impact Protection System)',\n      '3D contoured removable/washable liner',\n      'Dial-adjustable headband',\n      'Extended rear coverage to protect the base of the skull and neck',\n      'SEI-certified (International Equestrian Safety Standard)'\n    ],\n    image: '/images/products/horse-riding-helmet.jpg',\n    category: 'Equestrian'\n  }\n];\n\nexport const services: Service[] = [\n  {\n    id: 'oem',\n    title: 'OEM Manufacturing',\n    description: 'We provide full Original Equipment Manufacturing services, producing helmets according to your exact specifications and branding them with your logo and design elements.',\n    icon: 'factory'\n  },\n  {\n    id: 'odm',\n    title: 'ODM Solutions',\n    description: 'Our Original Design Manufacturing service offers ready-made helmet designs that can be customized with your branding, saving you time and development costs.',\n    icon: 'tool'\n  },\n  {\n    id: 'design',\n    title: 'Custom Design',\n    description: 'Our experienced design team works closely with you to create unique helmet designs that meet your specific requirements and stand out in the market.',\n    icon: 'design'\n  },\n  {\n    id: 'rd',\n    title: 'R&D Capabilities',\n    description: 'We invest heavily in research and development to create innovative helmet technologies and designs that improve safety, comfort, and performance.',\n    icon: 'experiment'\n  },\n  {\n    id: 'quality',\n    title: 'Quality Assurance',\n    description: 'Our rigorous quality control processes ensure that all helmets meet or exceed international safety standards and your specific requirements.',\n    icon: 'safety'\n  },\n  {\n    id: 'logistics',\n    title: 'Global Logistics',\n    description: 'We handle all aspects of shipping and logistics, ensuring your products are delivered on time and in perfect condition anywhere in the world.',\n    icon: 'global'\n  }\n];\n\nexport const factoryFeatures: FactoryFeature[] = [\n  {\n    id: 'manufacturing',\n    title: 'State-of-the-Art Manufacturing',\n    description: 'Our modern manufacturing facility is equipped with advanced production lines and quality control systems to ensure the highest standards for all our products.',\n    image: '/images/factory/manufacturing.jpg'\n  },\n  {\n    id: 'rd-lab',\n    title: 'R&D Capabilities',\n    description: 'Our dedicated research and development team continuously works on new designs and technologies to improve helmet safety, comfort, and performance.',\n    image: '/images/factory/rd-lab.jpg'\n  },\n  {\n    id: 'quality-control',\n    title: 'Quality Control',\n    description: 'We implement strict quality control procedures at every stage of production to ensure all helmets meet international safety standards and customer specifications.',\n    image: '/images/factory/quality-control.jpg'\n  },\n  {\n    id: 'customization',\n    title: 'Customization Workshop',\n    description: 'Our specialized customization workshop handles all aspects of helmet personalization, from graphics and colors to special features and accessories.',\n    image: '/images/factory/customization.jpg'\n  }\n];\n\nexport const contactInfo: ContactInfo = {\n  address: 'Building B, South Securities Building, 1916B, Jianshe Road, Jiabei Community, Nanhu Street, Luohu District, Shenzhen, China',\n  phone: '+8613509806025',\n  email: '<EMAIL>',\n  whatsapp: '+8613509806025'\n};\n"], "mappings": "AAEA,OAAO,MAAMA,QAAmB,GAAG,CACjC;EACEC,EAAE,EAAE,cAAc;EAClBC,IAAI,EAAE,qBAAqB;EAC3BC,WAAW,EAAE,wOAAwO;EACrPC,QAAQ,EAAE,CACR,+BAA+B,EAC/B,qDAAqD,EACrD,wCAAwC,EACxC,6CAA6C,EAC7C,mDAAmD,EACnD,+CAA+C,EAC/C,yCAAyC,CAC1C;EACDC,KAAK,EAAE,mCAAmC;EAC1CC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,EAAE,EAAE,eAAe;EACnBC,IAAI,EAAE,sBAAsB;EAC5BC,WAAW,EAAE,oLAAoL;EACjMC,QAAQ,EAAE,CACR,sCAAsC,EACtC,6BAA6B,EAC7B,iCAAiC,EACjC,4BAA4B,EAC5B,qDAAqD,EACrD,uDAAuD,EACvD,kCAAkC,EAClC,8BAA8B,EAC9B,+BAA+B,CAChC;EACDC,KAAK,EAAE,oCAAoC;EAC3CC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,EAAE,EAAE,aAAa;EACjBC,IAAI,EAAE,oBAAoB;EAC1BC,WAAW,EAAE,6KAA6K;EAC1LC,QAAQ,EAAE,CACR,iCAAiC,EACjC,qDAAqD,EACrD,gCAAgC,EAChC,kCAAkC,EAClC,0DAA0D,EAC1D,8DAA8D,EAC9D,iDAAiD,EACjD,+CAA+C,CAChD;EACDC,KAAK,EAAE,kCAAkC;EACzCC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,EAAE,EAAE,cAAc;EAClBC,IAAI,EAAE,sBAAsB;EAC5BC,WAAW,EAAE,6LAA6L;EAC1MC,QAAQ,EAAE,CACR,wEAAwE,EACxE,uCAAuC,EACvC,qDAAqD,EACrD,yDAAyD,EACzD,qCAAqC,EACrC,0DAA0D,CAC3D;EACDC,KAAK,EAAE,mCAAmC;EAC1CC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,EAAE,EAAE,mBAAmB;EACvBC,IAAI,EAAE,mBAAmB;EACzBC,WAAW,EAAE,wFAAwF;EACrGC,QAAQ,EAAE,CACR,oDAAoD,EACpD,kCAAkC,EAClC,oCAAoC,EACpC,+BAA+B,EAC/B,yDAAyD,CAC1D;EACDC,KAAK,EAAE,wCAAwC;EAC/CC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,EAAE,EAAE,aAAa;EACjBC,IAAI,EAAE,aAAa;EACnBC,WAAW,EAAE,wFAAwF;EACrGC,QAAQ,EAAE,CACR,gDAAgD,EAChD,+DAA+D,EAC/D,kDAAkD,EAClD,wDAAwD,CACzD;EACDC,KAAK,EAAE,kCAAkC;EACzCC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,EAAE,EAAE,mBAAmB;EACvBC,IAAI,EAAE,mBAAmB;EACzBC,WAAW,EAAE,4FAA4F;EACzGC,QAAQ,EAAE,CACR,kEAAkE,EAClE,0DAA0D,EAC1D,2BAA2B,EAC3B,kEAAkE,EAClE,yBAAyB,EACzB,8BAA8B,CAC/B;EACDC,KAAK,EAAE,wCAAwC;EAC/CC,QAAQ,EAAE;AACZ,CAAC,EACD;EACEL,EAAE,EAAE,qBAAqB;EACzBC,IAAI,EAAE,mBAAmB;EACzBC,WAAW,EAAE,wFAAwF;EACrGC,QAAQ,EAAE,CACR,mDAAmD,EACnD,uCAAuC,EACvC,0BAA0B,EAC1B,kEAAkE,EAClE,0DAA0D,CAC3D;EACDC,KAAK,EAAE,0CAA0C;EACjDC,QAAQ,EAAE;AACZ,CAAC,CACF;AAED,OAAO,MAAMC,QAAmB,GAAG,CACjC;EACEN,EAAE,EAAE,KAAK;EACTO,KAAK,EAAE,mBAAmB;EAC1BL,WAAW,EAAE,2KAA2K;EACxLM,IAAI,EAAE;AACR,CAAC,EACD;EACER,EAAE,EAAE,KAAK;EACTO,KAAK,EAAE,eAAe;EACtBL,WAAW,EAAE,8JAA8J;EAC3KM,IAAI,EAAE;AACR,CAAC,EACD;EACER,EAAE,EAAE,QAAQ;EACZO,KAAK,EAAE,eAAe;EACtBL,WAAW,EAAE,sJAAsJ;EACnKM,IAAI,EAAE;AACR,CAAC,EACD;EACER,EAAE,EAAE,IAAI;EACRO,KAAK,EAAE,kBAAkB;EACzBL,WAAW,EAAE,mJAAmJ;EAChKM,IAAI,EAAE;AACR,CAAC,EACD;EACER,EAAE,EAAE,SAAS;EACbO,KAAK,EAAE,mBAAmB;EAC1BL,WAAW,EAAE,8IAA8I;EAC3JM,IAAI,EAAE;AACR,CAAC,EACD;EACER,EAAE,EAAE,WAAW;EACfO,KAAK,EAAE,kBAAkB;EACzBL,WAAW,EAAE,+IAA+I;EAC5JM,IAAI,EAAE;AACR,CAAC,CACF;AAED,OAAO,MAAMC,eAAiC,GAAG,CAC/C;EACET,EAAE,EAAE,eAAe;EACnBO,KAAK,EAAE,gCAAgC;EACvCL,WAAW,EAAE,gKAAgK;EAC7KE,KAAK,EAAE;AACT,CAAC,EACD;EACEJ,EAAE,EAAE,QAAQ;EACZO,KAAK,EAAE,kBAAkB;EACzBL,WAAW,EAAE,oJAAoJ;EACjKE,KAAK,EAAE;AACT,CAAC,EACD;EACEJ,EAAE,EAAE,iBAAiB;EACrBO,KAAK,EAAE,iBAAiB;EACxBL,WAAW,EAAE,oKAAoK;EACjLE,KAAK,EAAE;AACT,CAAC,EACD;EACEJ,EAAE,EAAE,eAAe;EACnBO,KAAK,EAAE,wBAAwB;EAC/BL,WAAW,EAAE,qJAAqJ;EAClKE,KAAK,EAAE;AACT,CAAC,CACF;AAED,OAAO,MAAMM,WAAwB,GAAG;EACtCC,OAAO,EAAE,6HAA6H;EACtIC,KAAK,EAAE,gBAAgB;EACvBC,KAAK,EAAE,sBAAsB;EAC7BC,QAAQ,EAAE;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}