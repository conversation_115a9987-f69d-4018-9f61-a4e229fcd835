import React, { useState, useEffect } from 'react';
import { Typo<PERSON>, <PERSON>, Col, Card, Button, Tabs, Tag, List, Divider, Space, Input } from 'antd';
import { SearchOutlined, CheckOutlined } from '@ant-design/icons';
import { products } from '../models/mockData';
import { Product } from '../models/types';
import { getImagePath } from '../utils/imageUtils';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Meta } = Card;
const { Search } = Input;

const ProductsPage: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string>('All');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products);
  
  // Get unique categories
  const categories = ['All', ...Array.from(new Set(products.map(product => product.category)))];
  
  // Filter products based on category and search query
  useEffect(() => {
    let filtered = products;
    
    // Filter by category
    if (activeCategory !== 'All') {
      filtered = filtered.filter(product => product.category === activeCategory);
    }
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        product => 
          product.name.toLowerCase().includes(query) || 
          product.description.toLowerCase().includes(query)
      );
    }
    
    setFilteredProducts(filtered);
  }, [activeCategory, searchQuery]);

  return (
    <div className="page-container">
      {/* Page Header */}
      <div style={{ textAlign: 'center', marginBottom: 48 }}>
        <Title>Our Products</Title>
        <Paragraph style={{ fontSize: 16, maxWidth: 800, margin: '0 auto' }}>
          Explore our wide range of high-quality helmets designed for various sports and activities.
          All our helmets are manufactured to meet or exceed international safety standards.
        </Paragraph>
      </div>

      {/* Search and Filter */}
      <div style={{ marginBottom: 32 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} md={8}>
            <Search
              placeholder="Search products"
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              onSearch={(value) => setSearchQuery(value)}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </Col>
          <Col xs={24} md={16}>
            <Tabs 
              activeKey={activeCategory} 
              onChange={setActiveCategory}
              type="card"
              size="large"
              style={{ marginBottom: 0 }}
            >
              {categories.map(category => (
                <TabPane tab={category} key={category} />
              ))}
            </Tabs>
          </Col>
        </Row>
      </div>

      {/* Products Grid */}
      <Row gutter={[24, 32]}>
        {filteredProducts.length > 0 ? (
          filteredProducts.map(product => (
            <Col xs={24} sm={12} lg={8} key={product.id} id={product.id}>
              <Card
                hoverable
                className="product-card"
                cover={<img alt={product.name} src={getImagePath(product.image)} />}
              >
                <Meta 
                  title={
                    <Space>
                      {product.name}
                      <Tag color="#0056b3">{product.category}</Tag>
                    </Space>
                  } 
                  description={product.description} 
                />
                <Divider />
                <Title level={5}>Key Features:</Title>
                <List
                  size="small"
                  dataSource={product.features.slice(0, 4)}
                  renderItem={item => (
                    <List.Item>
                      <CheckOutlined style={{ color: '#52c41a', marginRight: 8 }} /> {item}
                    </List.Item>
                  )}
                />
                {product.features.length > 4 && (
                  <Paragraph style={{ marginTop: 8 }}>
                    <Button type="link" style={{ padding: 0 }}>
                      +{product.features.length - 4} more features
                    </Button>
                  </Paragraph>
                )}
                <div style={{ marginTop: 16 }}>
                  <Button type="primary" block>
                    Request Quote
                  </Button>
                </div>
              </Card>
            </Col>
          ))
        ) : (
          <Col span={24} style={{ textAlign: 'center', padding: '40px 0' }}>
            <Title level={4}>No products found matching your criteria.</Title>
            <Paragraph>
              Please try a different search term or category.
            </Paragraph>
            <Button 
              type="primary" 
              onClick={() => {
                setActiveCategory('All');
                setSearchQuery('');
              }}
            >
              Reset Filters
            </Button>
          </Col>
        )}
      </Row>

      {/* Custom Helmet Section */}
      <section className="section" style={{ marginTop: 64, background: '#f5f5f5', padding: 32, borderRadius: 8 }}>
        <Row gutter={[48, 24]} align="middle">
          <Col xs={24} md={12}>
            <img 
              src="/images/products/custom-helmet.jpg" 
              alt="Custom Helmet" 
              style={{ width: '100%', borderRadius: 8 }} 
            />
          </Col>
          <Col xs={24} md={12}>
            <Title level={2}>Need a Custom Helmet Solution?</Title>
            <Paragraph style={{ fontSize: 16, marginBottom: 16 }}>
              We specialize in OEM and ODM helmet manufacturing, offering customized solutions to meet your specific requirements.
            </Paragraph>
            <Paragraph style={{ fontSize: 16, marginBottom: 16 }}>
              Whether you need a unique design, special features, or custom branding, our experienced team can bring your vision to life.
            </Paragraph>
            <Paragraph style={{ fontSize: 16, marginBottom: 24 }}>
              Contact us today to discuss your custom helmet project and discover how we can help you create the perfect helmet for your brand.
            </Paragraph>
            <Button type="primary" size="large">
              Contact Us for Custom Solutions
            </Button>
          </Col>
        </Row>
      </section>

      {/* Safety Standards Section */}
      <section className="section" style={{ marginTop: 64 }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>
          Safety Standards & Certifications
        </Title>
        <Paragraph style={{ textAlign: 'center', fontSize: 16, maxWidth: 800, margin: '0 auto 32px' }}>
          All our helmets are designed and manufactured to meet or exceed international safety standards,
          ensuring the highest level of protection for users.
        </Paragraph>
        <Row gutter={[24, 24]} justify="center">
          <Col xs={12} sm={6} md={4} style={{ textAlign: 'center' }}>
            <img src="/images/certifications/ce.png" alt="CE Certification" style={{ height: 80, marginBottom: 8 }} />
            <Paragraph strong>CE Certified</Paragraph>
          </Col>
          <Col xs={12} sm={6} md={4} style={{ textAlign: 'center' }}>
            <img src="/images/certifications/astm.png" alt="ASTM Certification" style={{ height: 80, marginBottom: 8 }} />
            <Paragraph strong>ASTM Certified</Paragraph>
          </Col>
          <Col xs={12} sm={6} md={4} style={{ textAlign: 'center' }}>
            <img src="/images/certifications/iso.png" alt="ISO Certification" style={{ height: 80, marginBottom: 8 }} />
            <Paragraph strong>ISO 9001</Paragraph>
          </Col>
          <Col xs={12} sm={6} md={4} style={{ textAlign: 'center' }}>
            <img src="/images/certifications/dot.png" alt="DOT Certification" style={{ height: 80, marginBottom: 8 }} />
            <Paragraph strong>DOT/ECE</Paragraph>
          </Col>
        </Row>
      </section>
    </div>
  );
};

export default ProductsPage;
