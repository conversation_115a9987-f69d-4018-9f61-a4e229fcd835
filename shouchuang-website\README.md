# XLL Sporting Goods Co., Ltd. Website

This is a React + TypeScript + Ant Design Pro website for XLL Sporting Goods Co., Ltd. (formerly C ORIGINALS), a professional helmet manufacturer specializing in OEM/ODM services.

## Project Overview

This website showcases the company's products, manufacturing capabilities, and services, with a focus on their expertise in helmet design and production.

### Key Features

- Responsive design for all devices
- Product showcase with filtering and search
- Detailed information about manufacturing capabilities
- OEM/ODM services information
- Contact form for inquiries
- Multi-language support (English/Chinese)
- Video player component for company introduction

## Project Structure

```
shouchuang-website/
├── public/               # Public assets
├── src/                  # Source code
│   ├── assets/           # Images, fonts, etc.
│   ├── components/       # Reusable components
│   ├── layouts/          # Layout components
│   ├── models/           # TypeScript interfaces and mock data
│   ├── pages/            # Page components
│   ├── services/         # API services
│   ├── utils/            # Utility functions
│   ├── App.tsx           # Main App component
│   ├── index.tsx         # Entry point
│   └── ...
├── package.json          # Dependencies and scripts
├── tsconfig.json         # TypeScript configuration
└── README.md             # Project documentation
```

## Pages

1. **Home** - Landing page with hero section, featured products, and company overview
2. **About** - Company information, history, mission, and values
3. **Products** - Product catalog with filtering and search functionality
4. **Factory** - Information about manufacturing capabilities and facilities
5. **Services** - Details about OEM/ODM services offered
6. **Contact** - Contact form and company contact information

## Technologies Used

- React
- TypeScript
- Ant Design Pro
- React Router
- CSS Modules

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
   or
   ```
   yarn install
   ```

3. Start the development server:
   ```
   npm start
   ```
   or
   ```
   yarn start
   ```

4. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Building for Production

```
npm run build
```
or
```
yarn build
```

This will create an optimized production build in the `build` folder.

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

## Contact

For any questions or inquiries about this project, please contact:

- Email: [<EMAIL>](mailto:<EMAIL>)
- Phone: +86 123 4567 8910
