import React, { useState } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import { Layout, Menu, But<PERSON>, Drawer, Row, Col, Typography, Space } from 'antd';
import {
  HomeOutlined,
  InfoCircleOutlined,
  AppstoreOutlined,
  BuildOutlined,
  ToolOutlined,
  ContactsOutlined,
  MenuOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import WhatsAppContact from '../components/WhatsAppContact';

const { Header, Content, Footer } = Layout;
const { Title, Text } = Typography;

const MainLayout: React.FC = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const location = useLocation();
  const currentPath = location.pathname;

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: <Link to="/">Home</Link>,
    },
    {
      key: '/about',
      icon: <InfoCircleOutlined />,
      label: <Link to="/about">About Us</Link>,
    },
    {
      key: '/products',
      icon: <AppstoreOutlined />,
      label: <Link to="/products">Products</Link>,
    },
    {
      key: '/factory',
      icon: <BuildOutlined />,
      label: <Link to="/factory">Factory</Link>,
    },
    {
      key: '/services',
      icon: <ToolOutlined />,
      label: <Link to="/services">OEM/ODM</Link>,
    },
    {
      key: '/contact',
      icon: <ContactsOutlined />,
      label: <Link to="/contact">Contact</Link>,
    },
  ];

  return (
    <Layout className="layout">
      <Header style={{ position: 'sticky', top: 0, zIndex: 1, width: '100%', padding: '0 24px', background: '#fff', boxShadow: '0 2px 8px rgba(0,0,0,0.06)' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Link to="/">
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <img src="/logo/logo.jpg" alt="XLL Logo" style={{ height: 40, marginRight: 10 }} />
                <Title level={4} style={{ margin: 0, color: '#0056b3' }}>XLL</Title>
              </div>
            </Link>
          </Col>
          <Col xs={0} md={16}>
            <Menu
              mode="horizontal"
              selectedKeys={[currentPath]}
              items={menuItems}
              style={{ justifyContent: 'flex-end', border: 'none' }}
            />
          </Col>
          <Col xs={4} md={0} style={{ textAlign: 'right' }}>
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => setMobileMenuOpen(true)}
              style={{ fontSize: '16px' }}
            />
          </Col>
        </Row>
        <Drawer
          title="Menu"
          placement="right"
          onClose={() => setMobileMenuOpen(false)}
          open={mobileMenuOpen}
          width={250}
        >
          <Menu
            mode="vertical"
            selectedKeys={[currentPath]}
            items={menuItems}
            onClick={() => setMobileMenuOpen(false)}
          />
        </Drawer>
      </Header>
      <Content>
        <Outlet />
      </Content>
      <Footer style={{ background: '#001529', color: '#fff', padding: '40px 24px 24px' }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={8}>
            <Space direction="vertical" size="small">
              <Title level={4} style={{ color: '#fff', marginBottom: 16 }}>XLL</Title>
              <Text style={{ color: 'rgba(255,255,255,0.65)' }}>
                Professional OEM/ODM Helmet Manufacturer
              </Text>
              <WhatsAppContact
                imageSrc="/images/whatApp.jpg"
                altText="WhatsApp Contact QR Code"
                description="Contact us via WhatsApp:"
                style={{ marginTop: 16 }}
              />
              {/* <Text style={{ color: 'rgba(255,255,255,0.65)' }}>
                ShouChuang Sporting Goods Co., Ltd.
              </Text> */}
            </Space>
          </Col>
          <Col xs={24} sm={8}>
            <Title level={5} style={{ color: '#fff', marginBottom: 16 }}>Quick Links</Title>
            <Menu
              mode="vertical"
              theme="dark"
              style={{ background: 'transparent', border: 'none' }}
              items={menuItems}
            />
          </Col>
          <Col xs={24} sm={8}>
            <Title level={5} style={{ color: '#fff', marginBottom: 16 }}>Products</Title>
            <ul style={{ color: 'rgba(255,255,255,0.65)', paddingLeft: 0 }}>
              <li style={{ marginBottom: 8 }}>Road Cycling Helmet</li>
              <li style={{ marginBottom: 8 }}>Mountain Bike Helmet</li>
              <li style={{ marginBottom: 8 }}>Kids Helmet</li>
              <li style={{ marginBottom: 8 }}>Smart Helmet</li>
              <li style={{ marginBottom: 8 }}>Skateboard Helmet</li>
              <li style={{ marginBottom: 8 }}>Snow Helmet</li>
              <li style={{ marginBottom: 8 }}>Motorcycle Helmet</li>
              <li style={{ marginBottom: 8 }}>Horse Riding Helmet</li>
            </ul>
          </Col>
        </Row>
        <Row justify="space-between" align="middle" style={{ marginTop: 40, paddingTop: 20, borderTop: '1px solid rgba(255,255,255,0.1)' }}>
          <Col>
            <Text style={{ color: 'rgba(255,255,255,0.45)' }}>
              &copy; {new Date().getFullYear()} XLL Sporting Goods Co., Ltd. All Rights Reserved.
            </Text>
          </Col>
          <Col>
            <Space>
              <Button type="text" icon={<GlobalOutlined />} style={{ color: 'rgba(255,255,255,0.65)' }}>
                English
              </Button>
            </Space>
          </Col>
        </Row>
      </Footer>
    </Layout>
  );
};

export default MainLayout;

