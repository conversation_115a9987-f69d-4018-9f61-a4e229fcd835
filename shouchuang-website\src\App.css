.App {
  text-align: center;
}

.page-container {
  padding: 24px;
  min-height: calc(100vh - 64px - 70px);
}

.hero-section {
  height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #fff;
  position: relative;
  background-size: cover;
  background-position: center;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  padding: 0 20px;
}

.hero-title {
  font-size: 3.5rem;
  margin-bottom: 10px;
  text-transform: uppercase;
}

.hero-subtitle {
  font-size: 2rem;
  margin-bottom: 20px;
}

.hero-description {
  font-size: 1.2rem;
  margin-bottom: 30px;
}

.section {
  padding: 60px 0;
}

.section-dark {
  background-color: #f5f5f5;
}

.product-card {
  margin-bottom: 24px;
  transition: all 0.3s;
  height: 100%;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-card .ant-card-cover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 240px;
  overflow: hidden;
}

.product-card .ant-card-cover img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  transition: transform 0.3s ease;
}

.product-card:hover .ant-card-cover img {
  transform: scale(1.05);
}

.product-card .ant-card-body {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: calc(100% - 240px);
}

.product-card .ant-card-meta {
  flex-grow: 1;
}

.product-card .ant-card-meta-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.product-card .ant-card-meta-description {
  color: #666;
  line-height: 1.5;
}

/* Products page specific styles */
.page-container .product-card .ant-card-cover {
  height: 280px;
  padding: 24px;
}

.page-container .product-card .ant-card-body {
  height: auto;
  min-height: 300px;
}

.contact-form {
  max-width: 600px;
  margin: 0 auto;
}

.video-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.video-container:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.video-container video {
  width: 100%;
  height: auto;
  display: block;
  background-color: #000;
}

.video-container video::-webkit-media-controls-panel {
  background-color: rgba(0, 0, 0, 0.8);
}

.video-container video::-webkit-media-controls-play-button,
.video-container video::-webkit-media-controls-volume-slider,
.video-container video::-webkit-media-controls-timeline {
  filter: invert(1);
}

.whatsapp-image {
  width: 200px;
  height: auto;
  border-radius: 8px;
  border: 2px solid rgba(255,255,255,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.whatsapp-image:hover {
  border-color: rgba(255,255,255,0.3);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.contact-whatsapp .whatsapp-image {
  max-width: 120px;
  width: 100%;
  border: 2px solid #e0e0e0;
}

.contact-whatsapp .whatsapp-image:hover {
  border-color: #25D366;
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .video-container {
    margin-bottom: 24px;
  }

  .whatsapp-image {
    width: 150px;
  }

  .product-card .ant-card-cover {
    height: 200px;
    padding: 15px;
  }

  .product-card .ant-card-body {
    padding: 16px;
    height: calc(100% - 200px);
  }

  .page-container .product-card .ant-card-cover {
    height: 220px;
    padding: 16px;
  }

  .page-container .product-card .ant-card-body {
    min-height: 280px;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .video-container {
    margin-bottom: 32px;
    border-radius: 6px;
  }

  .video-container:hover {
    transform: none;
  }

  .whatsapp-image {
    width: 120px;
  }

  .whatsapp-image:hover {
    transform: none;
  }

  .product-card .ant-card-cover {
    height: 180px;
    padding: 12px;
  }

  .product-card .ant-card-body {
    padding: 12px;
    height: calc(100% - 180px);
  }

  .product-card:hover {
    transform: none;
  }

  .page-container .product-card .ant-card-cover {
    height: 200px;
    padding: 12px;
  }

  .page-container .product-card .ant-card-body {
    min-height: 260px;
  }
}

/* Contact Page Red Theme */
.contact-page-red {
  background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 50%, #fff5f5 100%);
  min-height: 100vh;
}

.contact-page-red .contact-header {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  padding: 60px 40px;
  margin: -24px -24px 48px -24px;
  border-radius: 0 0 20px 20px;
  box-shadow: 0 8px 32px rgba(220, 38, 38, 0.3);
}

.contact-page-red .contact-title {
  color: #ffffff !important;
  font-size: 3rem !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.contact-page-red .contact-subtitle {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 18px !important;
  font-weight: 400 !important;
}

/* Contact Info Cards */
.contact-page-red .contact-info-card {
  border: 2px solid #fecaca;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(220, 38, 38, 0.1);
  transition: all 0.3s ease;
  background: #ffffff;
}

.contact-page-red .contact-info-card:hover {
  border-color: #dc2626;
  box-shadow: 0 8px 32px rgba(220, 38, 38, 0.2);
  transform: translateY(-4px);
}

.contact-page-red .contact-icon {
  color: #dc2626 !important;
  background: #fef2f2;
  padding: 8px;
  border-radius: 50%;
}

.contact-page-red .contact-info-card .ant-typography-title {
  color: #1f2937 !important;
  font-weight: 600 !important;
}

.contact-page-red .contact-info-card .ant-typography {
  color: #4b5563 !important;
}

/* Form Section */
.contact-page-red .contact-form-section {
  background: #ffffff;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(220, 38, 38, 0.1);
  margin: 24px 0;
}

.contact-page-red .form-title {
  color: #dc2626 !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
}

.contact-page-red .form-description {
  color: #6b7280 !important;
  font-size: 16px !important;
}

/* Form Styling */
.contact-page-red .contact-form-red .ant-form-item-label > label {
  color: #374151 !important;
  font-weight: 600 !important;
}

.contact-page-red .contact-form-red .ant-input,
.contact-page-red .contact-form-red .ant-select-selector,
.contact-page-red .contact-form-red .ant-input-affix-wrapper {
  border: 2px solid #fecaca !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.contact-page-red .contact-form-red .ant-input:focus,
.contact-page-red .contact-form-red .ant-select-selector:focus,
.contact-page-red .contact-form-red .ant-input-affix-wrapper:focus,
.contact-page-red .contact-form-red .ant-input-focused,
.contact-page-red .contact-form-red .ant-select-focused .ant-select-selector {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

.contact-page-red .contact-submit-btn {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 16px rgba(220, 38, 38, 0.3) !important;
  transition: all 0.3s ease !important;
}

.contact-page-red .contact-submit-btn:hover {
  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 24px rgba(220, 38, 38, 0.4) !important;
}

/* CTA Section */
.contact-page-red .contact-cta-section {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
  color: #ffffff;
  box-shadow: 0 8px 32px rgba(220, 38, 38, 0.3);
}

.contact-page-red .cta-title {
  color: #ffffff !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
}

.contact-page-red .cta-description {
  color: rgba(255, 255, 255, 0.9) !important;
}

.contact-page-red .cta-btn-primary {
  background: #ffffff !important;
  color: #dc2626 !important;
  border: 2px solid #ffffff !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.contact-page-red .cta-btn-primary:hover {
  background: #fef2f2 !important;
  color: #b91c1c !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 24px rgba(255, 255, 255, 0.3) !important;
}

.contact-page-red .cta-btn-secondary {
  background: transparent !important;
  color: #ffffff !important;
  border: 2px solid rgba(255, 255, 255, 0.5) !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.contact-page-red .cta-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border-color: #ffffff !important;
  transform: translateY(-2px) !important;
}

/* WhatsApp Section in Contact Page */
.contact-page-red .ant-card {
  border: 2px solid #fecaca;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(220, 38, 38, 0.1);
}

.contact-page-red .ant-card:hover {
  border-color: #dc2626;
  box-shadow: 0 8px 32px rgba(220, 38, 38, 0.2);
}

.contact-page-red .ant-card .ant-typography-title {
  color: #dc2626 !important;
}

.contact-page-red .whatsapp-btn-red {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 16px rgba(220, 38, 38, 0.3) !important;
  transition: all 0.3s ease !important;
}

.contact-page-red .whatsapp-btn-red:hover {
  background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 24px rgba(220, 38, 38, 0.4) !important;
}

/* Divider */
.contact-page-red .ant-divider {
  border-color: #fecaca;
}

/* FAQ Section */
.contact-page-red .ant-typography-title {
  color: #1f2937 !important;
}

.contact-page-red .ant-typography {
  color: #4b5563 !important;
}

/* Responsive Design for Contact Page */
@media (max-width: 768px) {
  .contact-page-red .contact-header {
    padding: 40px 20px;
    margin: -24px -24px 32px -24px;
  }

  .contact-page-red .contact-title {
    font-size: 2.5rem !important;
  }

  .contact-page-red .contact-subtitle {
    font-size: 16px !important;
  }

  .contact-page-red .contact-form-section {
    padding: 24px;
    margin: 16px 0;
  }
}

@media (max-width: 576px) {
  .contact-page-red .contact-header {
    padding: 32px 16px;
  }

  .contact-page-red .contact-title {
    font-size: 2rem !important;
  }

  .contact-page-red .contact-form-section {
    padding: 20px;
  }
}
}
