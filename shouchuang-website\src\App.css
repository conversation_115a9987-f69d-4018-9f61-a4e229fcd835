.App {
  text-align: center;
}

.page-container {
  padding: 24px;
  min-height: calc(100vh - 64px - 70px);
}

.hero-section {
  height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #fff;
  position: relative;
  background-size: cover;
  background-position: center;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  padding: 0 20px;
}

.hero-title {
  font-size: 3.5rem;
  margin-bottom: 10px;
  text-transform: uppercase;
}

.hero-subtitle {
  font-size: 2rem;
  margin-bottom: 20px;
}

.hero-description {
  font-size: 1.2rem;
  margin-bottom: 30px;
}

.section {
  padding: 60px 0;
}

.section-dark {
  background-color: #f5f5f5;
}

.product-card {
  margin-bottom: 24px;
  transition: all 0.3s;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.contact-form {
  max-width: 600px;
  margin: 0 auto;
}

.video-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.video-container:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.video-container video {
  width: 100%;
  height: auto;
  display: block;
  background-color: #000;
}

.video-container video::-webkit-media-controls-panel {
  background-color: rgba(0, 0, 0, 0.8);
}

.video-container video::-webkit-media-controls-play-button,
.video-container video::-webkit-media-controls-volume-slider,
.video-container video::-webkit-media-controls-timeline {
  filter: invert(1);
}

.whatsapp-image {
  width: 200px;
  height: auto;
  border-radius: 8px;
  border: 2px solid rgba(255,255,255,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.whatsapp-image:hover {
  border-color: rgba(255,255,255,0.3);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .video-container {
    margin-bottom: 24px;
  }

  .whatsapp-image {
    width: 150px;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .video-container {
    margin-bottom: 32px;
    border-radius: 6px;
  }

  .video-container:hover {
    transform: none;
  }

  .whatsapp-image {
    width: 120px;
  }

  .whatsapp-image:hover {
    transform: none;
  }
}
