.App {
  text-align: center;
}

.page-container {
  padding: 24px;
  min-height: calc(100vh - 64px - 70px);
}

.hero-section {
  height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #fff;
  position: relative;
  background-size: cover;
  background-position: center;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  padding: 0 20px;
}

.hero-title {
  font-size: 3.5rem;
  margin-bottom: 10px;
  text-transform: uppercase;
}

.hero-subtitle {
  font-size: 2rem;
  margin-bottom: 20px;
}

.hero-description {
  font-size: 1.2rem;
  margin-bottom: 30px;
}

.section {
  padding: 60px 0;
}

.section-dark {
  background-color: #f5f5f5;
}

.product-card {
  margin-bottom: 24px;
  transition: all 0.3s;
  height: 100%;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-card .ant-card-cover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 240px;
  overflow: hidden;
}

.product-card .ant-card-cover img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  transition: transform 0.3s ease;
}

.product-card:hover .ant-card-cover img {
  transform: scale(1.05);
}

.product-card .ant-card-body {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: calc(100% - 240px);
}

.product-card .ant-card-meta {
  flex-grow: 1;
}

.product-card .ant-card-meta-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.product-card .ant-card-meta-description {
  color: #666;
  line-height: 1.5;
}

/* Products page specific styles */
.page-container .product-card .ant-card-cover {
  height: 280px;
  padding: 24px;
}

.page-container .product-card .ant-card-body {
  height: auto;
  min-height: 300px;
}

.contact-form {
  max-width: 600px;
  margin: 0 auto;
}

.video-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.video-container:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.video-container video {
  width: 100%;
  height: auto;
  display: block;
  background-color: #000;
}

.video-container video::-webkit-media-controls-panel {
  background-color: rgba(0, 0, 0, 0.8);
}

.video-container video::-webkit-media-controls-play-button,
.video-container video::-webkit-media-controls-volume-slider,
.video-container video::-webkit-media-controls-timeline {
  filter: invert(1);
}

.whatsapp-image {
  width: 200px;
  height: auto;
  border-radius: 8px;
  border: 2px solid rgba(255,255,255,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.whatsapp-image:hover {
  border-color: rgba(255,255,255,0.3);
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.contact-whatsapp .whatsapp-image {
  max-width: 120px;
  width: 100%;
  border: 2px solid #e0e0e0;
}

.contact-whatsapp .whatsapp-image:hover {
  border-color: #25D366;
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .video-container {
    margin-bottom: 24px;
  }

  .whatsapp-image {
    width: 150px;
  }

  .product-card .ant-card-cover {
    height: 200px;
    padding: 15px;
  }

  .product-card .ant-card-body {
    padding: 16px;
    height: calc(100% - 200px);
  }

  .page-container .product-card .ant-card-cover {
    height: 220px;
    padding: 16px;
  }

  .page-container .product-card .ant-card-body {
    min-height: 280px;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .video-container {
    margin-bottom: 32px;
    border-radius: 6px;
  }

  .video-container:hover {
    transform: none;
  }

  .whatsapp-image {
    width: 120px;
  }

  .whatsapp-image:hover {
    transform: none;
  }

  .product-card .ant-card-cover {
    height: 180px;
    padding: 12px;
  }

  .product-card .ant-card-body {
    padding: 12px;
    height: calc(100% - 180px);
  }

  .product-card:hover {
    transform: none;
  }

  .page-container .product-card .ant-card-cover {
    height: 200px;
    padding: 12px;
  }

  .page-container .product-card .ant-card-body {
    min-height: 260px;
  }
}
