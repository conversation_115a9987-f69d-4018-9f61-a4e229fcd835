# 视频功能设置指南

## 概述

本文档说明如何在 XLL 网站中设置和优化视频播放功能。

## 视频文件要求

### 文件格式
- **推荐格式**: MP4 (H.264 编码)
- **支持格式**: MP4, WebM, OGV
- **音频编码**: AAC (推荐) 或 MP3

### 文件大小和质量
- **最大文件大小**: 建议不超过 50MB
- **分辨率**: 
  - 桌面端: 1920x1080 (Full HD)
  - 移动端: 1280x720 (HD)
- **帧率**: 30fps 或 25fps
- **比特率**: 
  - 视频: 2-5 Mbps
  - 音频: 128-192 kbps

## 文件结构

```
public/
├── video/
│   ├── 20250714092754.mp4  # 主要介绍视频
│   └── [其他视频文件]
└── images/
    ├── about-img.jpg       # 视频封面图片
    └── [其他图片文件]
```

## 视频优化建议

### 1. 压缩视频文件

使用 FFmpeg 压缩视频：

```bash
# 压缩为 Web 优化的 MP4
ffmpeg -i input.mp4 -c:v libx264 -crf 23 -preset medium -c:a aac -b:a 128k -movflags +faststart output.mp4

# 创建多种分辨率版本
ffmpeg -i input.mp4 -vf scale=1920:1080 -c:v libx264 -crf 23 -preset medium -c:a aac -b:a 128k -movflags +faststart output_1080p.mp4
ffmpeg -i input.mp4 -vf scale=1280:720 -c:v libx264 -crf 23 -preset medium -c:a aac -b:a 128k -movflags +faststart output_720p.mp4
```

### 2. 创建封面图片

从视频中提取封面图片：

```bash
# 提取第一帧作为封面
ffmpeg -i input.mp4 -ss 00:00:01 -vframes 1 -q:v 2 poster.jpg

# 创建不同尺寸的封面
ffmpeg -i poster.jpg -vf scale=1920:1080 poster_1920x1080.jpg
ffmpeg -i poster.jpg -vf scale=1280:720 poster_1280x720.jpg
```

## 在 Ubuntu 上的部署注意事项

### 1. Nginx 配置

确保 Nginx 正确配置视频文件的 MIME 类型：

```nginx
server {
    # ... 其他配置

    # 视频文件配置
    location ~* \.(mp4|webm|ogg|avi|mov)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        add_header Accept-Ranges bytes;
        
        # 启用范围请求支持（用于视频拖拽）
        try_files $uri =404;
    }
    
    # 大文件上传限制
    client_max_body_size 100M;
}
```

### 2. Apache 配置

如果使用 Apache，添加以下配置：

```apache
<Directory "/var/www/shouchuang-website">
    # 视频文件缓存
    <FilesMatch "\.(mp4|webm|ogg|avi|mov)$">
        ExpiresActive On
        ExpiresDefault "access plus 30 days"
        Header set Cache-Control "public"
    </FilesMatch>
</Directory>

# 启用范围请求
LoadModule headers_module modules/mod_headers.so
Header set Accept-Ranges bytes
```

### 3. 文件权限

确保视频文件有正确的权限：

```bash
sudo chown -R www-data:www-data /var/www/shouchuang-website/video/
sudo chmod -R 644 /var/www/shouchuang-website/video/
```

## 性能优化

### 1. 启用 Gzip 压缩

虽然视频文件本身不需要 Gzip，但确保其他资源被压缩：

```nginx
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
```

### 2. CDN 配置

对于大型视频文件，建议使用 CDN：

```typescript
// 在 imageUtils.ts 中添加 CDN 支持
export const getVideoPath = (path: string): string => {
  const CDN_URL = process.env.REACT_APP_CDN_URL;
  
  if (CDN_URL && process.env.NODE_ENV === 'production') {
    return `${CDN_URL}${path}`;
  }
  
  return getImagePath(path);
};
```

## 故障排除

### 常见问题

1. **视频不播放**
   - 检查文件路径是否正确
   - 确认视频格式兼容性
   - 检查服务器 MIME 类型配置

2. **加载缓慢**
   - 压缩视频文件
   - 使用适当的 preload 设置
   - 考虑使用 CDN

3. **移动端播放问题**
   - 确保使用 H.264 编码
   - 添加 `playsinline` 属性
   - 检查移动端网络限制

### 调试命令

```bash
# 检查视频文件信息
ffprobe -v quiet -print_format json -show_format -show_streams video.mp4

# 测试视频文件完整性
ffmpeg -v error -i video.mp4 -f null - 2>error.log

# 检查服务器响应
curl -I http://your-domain.com/video/20250714092754.mp4
```

## 更新视频文件

要更新视频文件：

1. 将新视频文件上传到 `public/video/` 目录
2. 更新 Home.tsx 中的文件名引用
3. 重新构建应用：`npm run build`
4. 重新部署到服务器

## 联系支持

如果遇到视频相关问题，请联系技术支持：
- Email: <EMAIL>
- 提供具体的错误信息和浏览器控制台日志
