[{"C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\index.tsx": "1", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\App.tsx": "2", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\About.tsx": "3", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\layouts\\MainLayout.tsx": "4", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Home.tsx": "5", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Factory.tsx": "6", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Products.tsx": "7", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Services.tsx": "8", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Contact.tsx": "9", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\models\\mockData.ts": "10", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\utils\\imageUtils.ts": "11", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\components\\VideoPlayer.tsx": "12"}, {"size": 367, "mtime": 1746006127352, "results": "13", "hashOfConfig": "14"}, {"size": 1261, "mtime": 1746006138668, "results": "15", "hashOfConfig": "14"}, {"size": 9905, "mtime": 1746010541873, "results": "16", "hashOfConfig": "14"}, {"size": 5358, "mtime": 1746010523949, "results": "17", "hashOfConfig": "14"}, {"size": 9288, "mtime": 1752462036707, "results": "18", "hashOfConfig": "14"}, {"size": 10292, "mtime": 1746006396114, "results": "19", "hashOfConfig": "14"}, {"size": 8193, "mtime": 1746006354167, "results": "20", "hashOfConfig": "14"}, {"size": 13079, "mtime": 1746006445268, "results": "21", "hashOfConfig": "14"}, {"size": 10234, "mtime": 1746006486157, "results": "22", "hashOfConfig": "14"}, {"size": 8374, "mtime": 1746013214887, "results": "23", "hashOfConfig": "14"}, {"size": 544, "mtime": 1746013477092, "results": "24", "hashOfConfig": "14"}, {"size": 751, "mtime": 1752461912186, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1cyxzan", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\index.tsx", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\App.tsx", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\About.tsx", ["62", "63"], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\layouts\\MainLayout.tsx", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Home.tsx", ["64", "65"], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Factory.tsx", ["66", "67"], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Products.tsx", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Services.tsx", ["68", "69"], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Contact.tsx", ["70"], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\models\\mockData.ts", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\utils\\imageUtils.ts", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\components\\VideoPlayer.tsx", [], [], {"ruleId": "71", "severity": 1, "message": "72", "line": 2, "column": 57, "nodeType": "73", "messageId": "74", "endLine": 2, "endColumn": 62}, {"ruleId": "71", "severity": 1, "message": "75", "line": 6, "column": 3, "nodeType": "73", "messageId": "74", "endLine": 6, "endColumn": 15}, {"ruleId": "71", "severity": 1, "message": "76", "line": 3, "column": 46, "nodeType": "73", "messageId": "74", "endLine": 3, "endColumn": 54}, {"ruleId": "71", "severity": 1, "message": "77", "line": 3, "column": 63, "nodeType": "73", "messageId": "74", "endLine": 3, "endColumn": 70}, {"ruleId": "71", "severity": 1, "message": "72", "line": 2, "column": 58, "nodeType": "73", "messageId": "74", "endLine": 2, "endColumn": 63}, {"ruleId": "71", "severity": 1, "message": "78", "line": 13, "column": 9, "nodeType": "73", "messageId": "74", "endLine": 13, "endColumn": 13}, {"ruleId": "71", "severity": 1, "message": "79", "line": 2, "column": 68, "nodeType": "73", "messageId": "74", "endLine": 2, "endColumn": 73}, {"ruleId": "71", "severity": 1, "message": "78", "line": 17, "column": 9, "nodeType": "73", "messageId": "74", "endLine": 17, "endColumn": 13}, {"ruleId": "71", "severity": 1, "message": "80", "line": 12, "column": 27, "nodeType": "73", "messageId": "74", "endLine": 12, "endColumn": 31}, "@typescript-eslint/no-unused-vars", "'Image' is defined but never used.", "Identifier", "unusedVar", "'TeamOutlined' is defined but never used.", "'Carousel' is defined but never used.", "'Divider' is defined but never used.", "'Step' is assigned a value but never used.", "'Space' is defined but never used.", "'Text' is assigned a value but never used."]