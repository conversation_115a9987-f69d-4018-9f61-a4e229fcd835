[{"C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\index.tsx": "1", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\App.tsx": "2", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\About.tsx": "3", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\layouts\\MainLayout.tsx": "4", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Home.tsx": "5", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Factory.tsx": "6", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Products.tsx": "7", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Services.tsx": "8", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Contact.tsx": "9", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\models\\mockData.ts": "10", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\utils\\imageUtils.ts": "11", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\components\\VideoPlayer.tsx": "12", "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\components\\WhatsAppContact.tsx": "13"}, {"size": 367, "mtime": 1746006127352, "results": "14", "hashOfConfig": "15"}, {"size": 1261, "mtime": 1746006138668, "results": "16", "hashOfConfig": "15"}, {"size": 9905, "mtime": 1746010541873, "results": "17", "hashOfConfig": "15"}, {"size": 5662, "mtime": 1752463605455, "results": "18", "hashOfConfig": "15"}, {"size": 9288, "mtime": 1752462036707, "results": "19", "hashOfConfig": "15"}, {"size": 10292, "mtime": 1746006396114, "results": "20", "hashOfConfig": "15"}, {"size": 8193, "mtime": 1746006354167, "results": "21", "hashOfConfig": "15"}, {"size": 13079, "mtime": 1746006445268, "results": "22", "hashOfConfig": "15"}, {"size": 10310, "mtime": 1752464086630, "results": "23", "hashOfConfig": "15"}, {"size": 8375, "mtime": 1752463904789, "results": "24", "hashOfConfig": "15"}, {"size": 544, "mtime": 1746013477092, "results": "25", "hashOfConfig": "15"}, {"size": 751, "mtime": 1752461912186, "results": "26", "hashOfConfig": "15"}, {"size": 1934, "mtime": 1752463515796, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1cyxzan", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\index.tsx", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\App.tsx", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\About.tsx", ["67", "68"], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\layouts\\MainLayout.tsx", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Home.tsx", ["69", "70"], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Factory.tsx", ["71", "72"], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Products.tsx", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Services.tsx", ["73", "74"], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\pages\\Contact.tsx", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\models\\mockData.ts", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\utils\\imageUtils.ts", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\components\\VideoPlayer.tsx", [], [], "C:\\customized\\ShouChuang Sporting\\建站资料收集（最新版）\\shouchuang-website\\src\\components\\WhatsAppContact.tsx", [], [], {"ruleId": "75", "severity": 1, "message": "76", "line": 2, "column": 57, "nodeType": "77", "messageId": "78", "endLine": 2, "endColumn": 62}, {"ruleId": "75", "severity": 1, "message": "79", "line": 6, "column": 3, "nodeType": "77", "messageId": "78", "endLine": 6, "endColumn": 15}, {"ruleId": "75", "severity": 1, "message": "80", "line": 3, "column": 46, "nodeType": "77", "messageId": "78", "endLine": 3, "endColumn": 54}, {"ruleId": "75", "severity": 1, "message": "81", "line": 3, "column": 63, "nodeType": "77", "messageId": "78", "endLine": 3, "endColumn": 70}, {"ruleId": "75", "severity": 1, "message": "76", "line": 2, "column": 58, "nodeType": "77", "messageId": "78", "endLine": 2, "endColumn": 63}, {"ruleId": "75", "severity": 1, "message": "82", "line": 13, "column": 9, "nodeType": "77", "messageId": "78", "endLine": 13, "endColumn": 13}, {"ruleId": "75", "severity": 1, "message": "83", "line": 2, "column": 68, "nodeType": "77", "messageId": "78", "endLine": 2, "endColumn": 73}, {"ruleId": "75", "severity": 1, "message": "82", "line": 17, "column": 9, "nodeType": "77", "messageId": "78", "endLine": 17, "endColumn": 13}, "@typescript-eslint/no-unused-vars", "'Image' is defined but never used.", "Identifier", "unusedVar", "'TeamOutlined' is defined but never used.", "'Carousel' is defined but never used.", "'Divider' is defined but never used.", "'Step' is assigned a value but never used.", "'Space' is defined but never used."]