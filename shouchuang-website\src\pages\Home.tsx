import React from 'react';
import { Link } from 'react-router-dom';
import { Typography, Button, Row, Col, Card, Carousel, Space, Divider } from 'antd';
import { RightOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { products, services } from '../models/mockData';
import { getImagePath } from '../utils/imageUtils';
import VideoPlayer from '../components/VideoPlayer';

const { Title, Paragraph } = Typography;
const { Meta } = Card;

const HomePage: React.FC = () => {
  // Select featured products (first 4)
  const featuredProducts = products.slice(0, 4);

  // Select featured services (first 3)
  const featuredServices = services.slice(0, 3);

  return (
    <div>
      {/* Hero Section */}
      <div
        className="hero-section"
        style={{
          backgroundImage: `url(${getImagePath('/images/hero-bg.jpg')})`,
        }}
      >
        <div className="hero-content">
          <Title className="hero-title" style={{ color: '#fff' }}>
            XLL Sporting Goods Co., Ltd.
          </Title>
          {/* <Title level={2} className="hero-subtitle" style={{ color: '#fff' }}>
            ShouChuang Sporting Goods Co., Ltd.
          </Title> */}
          <Paragraph className="hero-description" style={{ color: '#fff' }}>
            Professional OEM/ODM Helmet Manufacturer
          </Paragraph>
          <Space>
            <Button type="primary" size="large">
              <Link to="/products">Explore Our Products</Link>
            </Button>
            <Button size="large">
              <Link to="/contact">Contact Us</Link>
            </Button>
          </Space>
        </div>
      </div>

      {/* About Section */}
      <section className="section">
        <div className="container">
          <Row gutter={[48, 24]} align="middle">
            <Col xs={24} md={12}>
              <VideoPlayer
                src="/video/20250714092754.mp4"
                poster="/images/about-img.jpg"
              />
            </Col>
            <Col xs={24} md={12}>
              <Title level={2}>About XLL</Title>
              <Paragraph style={{ fontSize: 16, marginBottom: 16 }}>
                XLL Sporting Goods Co., Ltd. is a professional helmet manufacturer specializing in the design, development, and production of high-quality helmets for various sports and activities.
              </Paragraph>
              <Paragraph style={{ fontSize: 16, marginBottom: 16 }}>
                With our state-of-the-art manufacturing facility and experienced R&D team, we provide exceptional OEM and ODM services to clients worldwide.
              </Paragraph>
              <Paragraph style={{ fontSize: 16, marginBottom: 24 }}>
                Our commitment to quality, innovation, and customer satisfaction has made us a trusted partner for sports equipment brands globally.
              </Paragraph>
              <Button type="primary" size="large">
                <Link to="/about">Learn More About Us <RightOutlined /></Link>
              </Button>
            </Col>
          </Row>
        </div>
      </section>

      {/* Products Section */}
      <section className="section section-dark">
        <div className="container">
          <Title level={2} style={{ textAlign: 'center', marginBottom: 48 }}>Our Products</Title>
          <Row gutter={[24, 24]}>
            {featuredProducts.map(product => (
              <Col xs={24} sm={12} lg={6} key={product.id}>
                <Card
                  hoverable
                  className="product-card"
                  cover={<img alt={product.name} src={getImagePath(product.image)} style={{ height: 200, objectFit: 'cover' }} />}
                >
                  <Meta
                    title={product.name}
                    description={product.description.length > 100 ? `${product.description.substring(0, 100)}...` : product.description}
                  />
                  <div style={{ marginTop: 16 }}>
                    <Button type="primary">
                      <Link to={`/products#${product.id}`}>View Details</Link>
                    </Button>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
          <div style={{ textAlign: 'center', marginTop: 40 }}>
            <Button type="primary" size="large">
              <Link to="/products">View All Products <RightOutlined /></Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="section">
        <div className="container">
          <Title level={2} style={{ textAlign: 'center', marginBottom: 16 }}>OEM & ODM Services</Title>
          <Paragraph style={{ textAlign: 'center', fontSize: 16, maxWidth: 800, margin: '0 auto 48px' }}>
            We provide comprehensive OEM and ODM services for helmet manufacturing, offering customized solutions to meet your specific requirements.
          </Paragraph>
          <Row gutter={[24, 24]}>
            {featuredServices.map(service => (
              <Col xs={24} md={8} key={service.id}>
                <Card className="service-card" style={{ height: '100%' }}>
                  <Title level={4}>{service.title}</Title>
                  <Paragraph>{service.description}</Paragraph>
                </Card>
              </Col>
            ))}
          </Row>
          <div style={{ textAlign: 'center', marginTop: 40 }}>
            <Button type="primary" size="large">
              <Link to="/services">Learn More About Our Services <RightOutlined /></Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="section section-dark">
        <div className="container">
          <Title level={2} style={{ textAlign: 'center', marginBottom: 48 }}>Why Choose Us</Title>
          <Row gutter={[24, 24]}>
            <Col xs={24} md={8}>
              <Card style={{ height: '100%' }}>
                <Title level={4}>Quality Assurance</Title>
                <Paragraph>
                  All our helmets undergo rigorous testing to meet or exceed international safety standards, ensuring the highest quality products for your brand.
                </Paragraph>
                <Space direction="vertical">
                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> CE Certified</Paragraph>
                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> ASTM/SEI Certified</Paragraph>
                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> DOT/ECE Certified</Paragraph>
                </Space>
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card style={{ height: '100%' }}>
                <Title level={4}>Customization Options</Title>
                <Paragraph>
                  We offer extensive customization options for all our helmet models, allowing you to create unique products that align with your brand identity.
                </Paragraph>
                <Space direction="vertical">
                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Custom Colors & Graphics</Paragraph>
                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Custom Packaging</Paragraph>
                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Custom Features & Accessories</Paragraph>
                </Space>
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card style={{ height: '100%' }}>
                <Title level={4}>Competitive Advantage</Title>
                <Paragraph>
                  Partner with us to gain a competitive edge in the market with innovative helmet designs, cost-effective manufacturing, and reliable delivery.
                </Paragraph>
                <Space direction="vertical">
                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Innovative Designs</Paragraph>
                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Competitive Pricing</Paragraph>
                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> On-time Delivery</Paragraph>
                </Space>
              </Card>
            </Col>
          </Row>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section" style={{ background: '#0056b3', color: '#fff' }}>
        <div className="container" style={{ textAlign: 'center' }}>
          <Title level={2} style={{ color: '#fff', marginBottom: 24 }}>Ready to Start Your Project?</Title>
          <Paragraph style={{ fontSize: 16, marginBottom: 32, color: '#fff' }}>
            Contact us today to discuss your helmet manufacturing needs and discover how we can help bring your ideas to life.
          </Paragraph>
          <Button size="large" style={{ background: '#fff', color: '#0056b3' }}>
            <Link to="/contact">Contact Us Now</Link>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
