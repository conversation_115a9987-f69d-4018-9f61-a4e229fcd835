import React from 'react';
import { getImagePath } from '../utils/imageUtils';

interface VideoPlayerProps {
  src: string;
  poster?: string;
  className?: string;
  style?: React.CSSProperties;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  src,
  poster,
  className = '',
  style = {}
}) => {
  return (
    <div
      className={`video-container ${className}`}
      style={style}
    >
      <video
        controls
        poster={poster ? getImagePath(poster) : undefined}
        preload="metadata"
        style={{ width: '100%', height: 'auto' }}
      >
        <source src={getImagePath(src)} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
    </div>
  );
};

export default VideoPlayer;
