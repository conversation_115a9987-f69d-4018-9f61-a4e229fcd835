import React from 'react';
import { Typography, Row, Col, Form, Input, Button, Card, Space, Divider, Select, message } from 'antd';
import {
  EnvironmentOutlined,
  PhoneOutlined,
  MailOutlined,
  WhatsAppOutlined,
  SendOutlined
} from '@ant-design/icons';
import { contactInfo } from '../models/mockData';
import WhatsAppContact from '../components/WhatsAppContact';

const { Title, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const ContactPage: React.FC = () => {
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    console.log('Form values:', values);
    message.success('Your message has been sent successfully! We will contact you soon.');
    form.resetFields();
  };

  return (
    <div className="page-container">
      {/* Page Header */}
      <div style={{ textAlign: 'center', marginBottom: 48 }}>
        <Title>Contact Us</Title>
        <Paragraph style={{ fontSize: 16, maxWidth: 800, margin: '0 auto' }}>
          Get in touch with our team to discuss your helmet manufacturing needs or request a quote for your project.
        </Paragraph>
      </div>

      {/* Contact Information */}
      <section className="section">
        <Row gutter={[24, 24]}>
          <Col xs={24} md={8}>
            <Card style={{ height: '100%' }}>
              <Space align="center" style={{ marginBottom: 16 }}>
                <EnvironmentOutlined style={{ fontSize: 24, color: '#0056b3' }} />
                <Title level={4} style={{ margin: 0 }}>Address</Title>
              </Space>
              <Paragraph style={{ fontSize: 16 }}>
                {contactInfo.address}
              </Paragraph>
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card style={{ height: '100%' }}>
              <Space align="center" style={{ marginBottom: 16 }}>
                <PhoneOutlined style={{ fontSize: 24, color: '#0056b3' }} />
                <Title level={4} style={{ margin: 0 }}>Phone</Title>
              </Space>
              <Paragraph style={{ fontSize: 16 }}>
                {contactInfo.phone}
              </Paragraph>
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card style={{ height: '100%' }}>
              <Space align="center" style={{ marginBottom: 16 }}>
                <MailOutlined style={{ fontSize: 24, color: '#0056b3' }} />
                <Title level={4} style={{ margin: 0 }}>Email</Title>
              </Space>
              <Paragraph style={{ fontSize: 16 }}>
                {contactInfo.email}
              </Paragraph>
            </Card>
          </Col>
        </Row>
      </section>

      <Divider />

      {/* Contact Form and Map */}
      <section className="section">
        <Row gutter={[48, 48]}>
          <Col xs={24} lg={14}>
            <Title level={2}>Send Us a Message</Title>
            <Paragraph style={{ marginBottom: 24 }}>
              Fill out the form below to get in touch with our team. We'll respond to your inquiry as soon as possible.
            </Paragraph>
            <Form
              form={form}
              name="contact_form"
              layout="vertical"
              onFinish={onFinish}
              requiredMark={false}
            >
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="name"
                    label="Your Name"
                    rules={[{ required: true, message: 'Please enter your name' }]}
                  >
                    <Input size="large" placeholder="Enter your name" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="email"
                    label="Email Address"
                    rules={[
                      { required: true, message: 'Please enter your email' },
                      { type: 'email', message: 'Please enter a valid email' }
                    ]}
                  >
                    <Input size="large" placeholder="Enter your email" />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="phone"
                    label="Phone Number"
                    rules={[{ required: true, message: 'Please enter your phone number' }]}
                  >
                    <Input size="large" placeholder="Enter your phone number" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="subject"
                    label="Subject"
                    rules={[{ required: true, message: 'Please select a subject' }]}
                  >
                    <Select size="large" placeholder="Select a subject">
                      <Option value="general">General Inquiry</Option>
                      <Option value="quote">Request a Quote</Option>
                      <Option value="oem">OEM Services</Option>
                      <Option value="odm">ODM Services</Option>
                      <Option value="sample">Sample Request</Option>
                      <Option value="other">Other</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item
                name="message"
                label="Message"
                rules={[{ required: true, message: 'Please enter your message' }]}
              >
                <TextArea 
                  rows={6} 
                  placeholder="Enter your message or inquiry details" 
                  size="large" 
                />
              </Form.Item>
              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  size="large" 
                  icon={<SendOutlined />}
                  style={{ minWidth: 150 }}
                >
                  Send Message
                </Button>
              </Form.Item>
            </Form>
          </Col>
          <Col xs={24} lg={10}>
            <Title level={2}>Our Location</Title>
            <div style={{ marginBottom: 24 }}>
              <img 
                src="/images/contact/map.jpg" 
                alt="Company Location Map" 
                style={{ width: '100%', borderRadius: 8 }} 
              />
            </div>
            <Card>
              <Title level={4}>Connect on WhatsApp</Title>
              <Row gutter={16} align="middle">
                <Col xs={24} md={16}>
                  <Paragraph style={{ marginBottom: 0 }}>
                    Scan the QR code or click the button below to connect with us on WhatsApp for quick responses to your inquiries.
                  </Paragraph>
                  <Button
                    type="primary"
                    icon={<WhatsAppOutlined />}
                    style={{ marginTop: 16, background: '#25D366', borderColor: '#25D366' }}
                    href={`https://wa.me/${contactInfo.whatsapp.replace(/[^0-9]/g, '')}`}
                    target="_blank"
                  >
                    Chat on WhatsApp
                  </Button>
                </Col>
                <Col xs={24} md={8} style={{ textAlign: 'center' }}>
                  <WhatsAppContact
                    imageSrc="/images/whatApp.jpg"
                    altText="WhatsApp QR Code"
                    description=""
                    className="contact-whatsapp"
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </section>

      <Divider />

      {/* FAQ Section */}
      <section className="section">
        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>Frequently Asked Questions</Title>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12}>
            <Title level={4}>What information should I provide for a quote?</Title>
            <Paragraph>
              To provide an accurate quote, please include details such as the type of helmet, quantity, specific features, customization requirements, and target market (for certification purposes).
            </Paragraph>
          </Col>
          <Col xs={24} md={12}>
            <Title level={4}>How can I request samples?</Title>
            <Paragraph>
              You can request samples by contacting us through the form above or via email. Please specify the helmet model, quantity of samples needed, and any customization requirements.
            </Paragraph>
          </Col>
          <Col xs={24} md={12}>
            <Title level={4}>What is your typical response time?</Title>
            <Paragraph>
              We typically respond to inquiries within 24-48 hours during business days. For urgent matters, we recommend contacting us via phone or WhatsApp.
            </Paragraph>
          </Col>
          <Col xs={24} md={12}>
            <Title level={4}>Do you offer factory tours?</Title>
            <Paragraph>
              Yes, we welcome clients to visit our factory. Please contact us in advance to schedule a tour so we can make proper arrangements for your visit.
            </Paragraph>
          </Col>
        </Row>
      </section>

      {/* CTA Section */}
      <section className="section" style={{ background: '#f5f5f5', padding: 32, borderRadius: 8, marginTop: 48, textAlign: 'center' }}>
        <Title level={2}>Ready to Start Your Project?</Title>
        <Paragraph style={{ fontSize: 16, maxWidth: 800, margin: '0 auto 24px' }}>
          Contact us today to discuss your helmet manufacturing needs and discover how we can help bring your ideas to life.
          Our team is ready to provide you with the information and support you need to make your project a success.
        </Paragraph>
        <Space size="large">
          <Button type="primary" size="large" icon={<MailOutlined />}>
            Email Us
          </Button>
          <Button size="large" icon={<PhoneOutlined />}>
            Call Us
          </Button>
        </Space>
      </section>
    </div>
  );
};

export default ContactPage;
