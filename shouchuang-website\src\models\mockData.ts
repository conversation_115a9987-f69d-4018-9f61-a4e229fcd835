import { Product, Service, FactoryFeature, ContactInfo } from './types';

export const products: Product[] = [
  {
    id: 'road-cycling',
    name: 'Road Cycling Helmet',
    description: 'Aerodynamic streamlined shape, wind tunnel test reduces wind resistance by 15%, helping to break through the speed. 22 three-dimensional ventilation ducts + internal guide grooves continuously take away heat and keep the head dry.',
    features: [
      'Aerodynamic streamlined shape',
      'Wind tunnel tested - reduces wind resistance by 15%',
      '22 three-dimensional ventilation ducts',
      'Internal guide grooves for heat dissipation',
      'Carbon fiber composite structure weighs only 240g',
      '360° adjustment system, fits Asian head shape',
      'UV protective coating lenses (optional)'
    ],
    image: '/images/products/road-cycling.jpg',
    category: 'Cycling'
  },
  {
    id: 'mountain-bike',
    name: 'Mountain Bike Helmet',
    description: 'The ultimate choice for all-terrain riders! Ultra-lightweight carbon fiber shell with honeycomb ventilation holes, high strength impact resistance and efficient heat dissipation.',
    features: [
      'Ultra-lightweight carbon fiber shell',
      'Honeycomb ventilation holes',
      'High strength impact resistance',
      'Efficient heat dissipation',
      'Rear extension design protects the back of the head',
      'Compatible with sports cameras and headlight brackets',
      'Detachable insect repellent mask',
      'EN 1078 safety certification',
      'Quick-release magnetic buckle'
    ],
    image: '/images/products/mountain-bike.jpg',
    category: 'Cycling'
  },
  {
    id: 'kids-helmet',
    name: 'Children\'s Helmet',
    description: 'Specially developed for children aged 3-12, the food-grade silicone lining is soft and skin-friendly, and the detachable and washable design solves the hygiene pain point.',
    features: [
      'Designed for children aged 3-12',
      'Food-grade silicone lining - soft and skin-friendly',
      'Detachable and washable design',
      'Cartoon IP co-branded appearance',
      'Back-head knob-type head circumference adjustment system',
      'Double-layer shell structure (ABS shell + high-density foam)',
      'Side reflective strips for nighttime visibility',
      'Electronic fence setup through APP (optional)'
    ],
    image: '/images/products/kids-helmet.jpg',
    category: 'Children'
  },
  {
    id: 'smart-helmet',
    name: 'Smart Bicycle Helmet',
    description: 'Designed for urban commuters and technology enthusiasts, it integrates cutting-edge intelligent technology: built-in LED warning light, Bluetooth connection, and collision sensing system.',
    features: [
      'Built-in LED warning light with automatic light sensitivity adjustment',
      'Bluetooth connection to mobile phones',
      'Real-time navigation and call reminders through APP',
      'Collision sensing system with automatic distress signal',
      'High-impact PC material outer shell',
      'Inner EPS buffer layer + MIPS anti-concussion technology'
    ],
    image: '/images/products/smart-helmet.jpg',
    category: 'Smart'
  },
  {
    id: 'skateboard-helmet',
    name: 'Skateboard Helmet',
    description: 'Lightweight yet protective helmet for skateboarding, rollerblading, and action sports.',
    features: [
      'ABS shell + PC inner layer, weighing only 350-400g',
      'Extended rear and ear protection',
      'Multi-directional airflow channels',
      'Sweat-absorbing inner padding',
      'Stylish colors + reflective strips for nighttime safety'
    ],
    image: '/images/products/skateboard-helmet.jpg',
    category: 'Action Sports'
  },
  {
    id: 'snow-helmet',
    name: 'Snow Helmet',
    description: 'Cold-weather helmet for skiing/snowboarding with integrated warmth and fog prevention.',
    features: [
      'Removable fleece liner compatible with goggles',
      'Hard ABS shell + soft inner layer for multi-impact protection',
      'Anti-fog ventilation + goggle clip compatibility',
      'Built-in speaker slots without compromising insulation'
    ],
    image: '/images/products/snow-helmet.jpg',
    category: 'Winter Sports'
  },
  {
    id: 'motorcycle-helmet',
    name: 'Motorcycle Helmet',
    description: 'Full-coverage helmet for road riders, optimized for high-speed safety and noise reduction.',
    features: [
      'Carbon fiber or composite fiber shell for lightweight durability',
      'Dual-density EPS liner + antibacterial removable padding',
      'Eyewear-compatible design',
      'Anti-fog/UV-resistant lens with quick-release replacement system',
      'DOT/ECE 22.06 certified',
      'Bluetooth headset compatible'
    ],
    image: '/images/products/motorcycle-helmet.jpg',
    category: 'Motorcycle'
  },
  {
    id: 'horse-riding-helmet',
    name: 'Equestrian Helmet',
    description: 'Professional helmet for equestrian sports, offering head/neck protection during falls.',
    features: [
      'MIPS (Multi-directional Impact Protection System)',
      '3D contoured removable/washable liner',
      'Dial-adjustable headband',
      'Extended rear coverage to protect the base of the skull and neck',
      'SEI-certified (International Equestrian Safety Standard)'
    ],
    image: '/images/products/horse-riding-helmet.jpg',
    category: 'Equestrian'
  }
];

export const services: Service[] = [
  {
    id: 'oem',
    title: 'OEM Manufacturing',
    description: 'We provide full Original Equipment Manufacturing services, producing helmets according to your exact specifications and branding them with your logo and design elements.',
    icon: 'factory'
  },
  {
    id: 'odm',
    title: 'ODM Solutions',
    description: 'Our Original Design Manufacturing service offers ready-made helmet designs that can be customized with your branding, saving you time and development costs.',
    icon: 'tool'
  },
  {
    id: 'design',
    title: 'Custom Design',
    description: 'Our experienced design team works closely with you to create unique helmet designs that meet your specific requirements and stand out in the market.',
    icon: 'design'
  },
  {
    id: 'rd',
    title: 'R&D Capabilities',
    description: 'We invest heavily in research and development to create innovative helmet technologies and designs that improve safety, comfort, and performance.',
    icon: 'experiment'
  },
  {
    id: 'quality',
    title: 'Quality Assurance',
    description: 'Our rigorous quality control processes ensure that all helmets meet or exceed international safety standards and your specific requirements.',
    icon: 'safety'
  },
  {
    id: 'logistics',
    title: 'Global Logistics',
    description: 'We handle all aspects of shipping and logistics, ensuring your products are delivered on time and in perfect condition anywhere in the world.',
    icon: 'global'
  }
];

export const factoryFeatures: FactoryFeature[] = [
  {
    id: 'manufacturing',
    title: 'State-of-the-Art Manufacturing',
    description: 'Our modern manufacturing facility is equipped with advanced production lines and quality control systems to ensure the highest standards for all our products.',
    image: '/images/factory/manufacturing.jpg'
  },
  {
    id: 'rd-lab',
    title: 'R&D Capabilities',
    description: 'Our dedicated research and development team continuously works on new designs and technologies to improve helmet safety, comfort, and performance.',
    image: '/images/factory/rd-lab.jpg'
  },
  {
    id: 'quality-control',
    title: 'Quality Control',
    description: 'We implement strict quality control procedures at every stage of production to ensure all helmets meet international safety standards and customer specifications.',
    image: '/images/factory/quality-control.jpg'
  },
  {
    id: 'customization',
    title: 'Customization Workshop',
    description: 'Our specialized customization workshop handles all aspects of helmet personalization, from graphics and colors to special features and accessories.',
    image: '/images/factory/customization.jpg'
  }
];

export const contactInfo: ContactInfo = {
  address: 'Building B, South Securities Building, 1916B, Jianshe Road, Jiabei Community, Nanhu Street, Luohu District, Shenzhen, China',
  phone: '+8613509806025',
  email: '<EMAIL>',
  whatsapp: '+8613509806025'
};
