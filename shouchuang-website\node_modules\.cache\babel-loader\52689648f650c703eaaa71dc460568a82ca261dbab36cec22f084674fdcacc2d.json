{"ast": null, "code": "var _jsxFileName = \"C:\\\\customized\\\\ShouChuang Sporting\\\\\\u5EFA\\u7AD9\\u8D44\\u6599\\u6536\\u96C6\\uFF08\\u6700\\u65B0\\u7248\\uFF09\\\\shouchuang-website\\\\src\\\\pages\\\\Home.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Typography, Button, Row, Col, Card, Space } from 'antd';\nimport { RightOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport { products, services } from '../models/mockData';\nimport { getImagePath } from '../utils/imageUtils';\nimport VideoPlayer from '../components/VideoPlayer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph\n} = Typography;\nconst {\n  Meta\n} = Card;\nconst HomePage = () => {\n  // Select featured products (first 4)\n  const featuredProducts = products.slice(0, 4);\n\n  // Select featured services (first 3)\n  const featuredServices = services.slice(0, 3);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-section\",\n      style: {\n        backgroundImage: `url(${getImagePath('/images/hero-bg.jpg')})`\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          className: \"hero-title\",\n          style: {\n            color: '#fff'\n          },\n          children: \"XLL Sporting Goods Co., Ltd.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          className: \"hero-description\",\n          style: {\n            color: '#fff'\n          },\n          children: \"Professional OEM/ODM Helmet Manufacturer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/products\",\n              children: \"Explore Our Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            size: \"large\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [48, 24],\n          align: \"middle\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(VideoPlayer, {\n              src: \"/video/20250714092754.mp4\"\n              // poster=\"/images/about-img.jpg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 2,\n              children: \"About XLL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                fontSize: 16,\n                marginBottom: 16\n              },\n              children: \"XLL Sporting Goods Co., Ltd. is a professional helmet manufacturer specializing in the design, development, and production of high-quality helmets for various sports and activities.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                fontSize: 16,\n                marginBottom: 16\n              },\n              children: \"With our state-of-the-art manufacturing facility and experienced R&D team, we provide exceptional OEM and ODM services to clients worldwide.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                fontSize: 16,\n                marginBottom: 24\n              },\n              children: \"Our commitment to quality, innovation, and customer satisfaction has made us a trusted partner for sports equipment brands globally.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              size: \"large\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/about\",\n                children: [\"Learn More About Us \", /*#__PURE__*/_jsxDEV(RightOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 55\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section section-dark\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            textAlign: 'center',\n            marginBottom: 48\n          },\n          children: \"Our Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [24, 24],\n          children: featuredProducts.map(product => /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            lg: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              hoverable: true,\n              className: \"product-card\",\n              cover: /*#__PURE__*/_jsxDEV(\"img\", {\n                alt: product.name,\n                src: getImagePath(product.image),\n                style: {\n                  height: 200,\n                  objectFit: 'cover'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 26\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(Meta, {\n                title: product.name,\n                description: product.description.length > 100 ? `${product.description.substring(0, 100)}...` : product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 16\n                },\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/products#${product.id}`,\n                    children: \"View Details\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 96,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this)\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            marginTop: 40\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/products\",\n              children: [\"View All Products \", /*#__PURE__*/_jsxDEV(RightOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 54\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            textAlign: 'center',\n            marginBottom: 16\n          },\n          children: \"OEM & ODM Services\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          style: {\n            textAlign: 'center',\n            fontSize: 16,\n            maxWidth: 800,\n            margin: '0 auto 48px'\n          },\n          children: \"We provide comprehensive OEM and ODM services for helmet manufacturing, offering customized solutions to meet your specific requirements.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [24, 24],\n          children: featuredServices.map(service => /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"service-card\",\n              style: {\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                children: service.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                children: service.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this)\n          }, service.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            marginTop: 40\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/services\",\n              children: [\"Learn More About Our Services \", /*#__PURE__*/_jsxDEV(RightOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 66\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section section-dark\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            textAlign: 'center',\n            marginBottom: 48\n          },\n          children: \"Why Choose Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [24, 24],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              style: {\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                children: \"Quality Assurance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                children: \"All our helmets undergo rigorous testing to meet or exceed international safety standards, ensuring the highest quality products for your brand.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 30\n                  }, this), \" CE Certified\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 30\n                  }, this), \" ASTM/SEI Certified\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 30\n                  }, this), \" DOT/ECE Certified\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              style: {\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                children: \"Customization Options\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                children: \"We offer extensive customization options for all our helmet models, allowing you to create unique products that align with your brand identity.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 30\n                  }, this), \" Custom Colors & Graphics\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 30\n                  }, this), \" Custom Packaging\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 30\n                  }, this), \" Custom Features & Accessories\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              style: {\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                children: \"Competitive Advantage\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                children: \"Partner with us to gain a competitive edge in the market with innovative helmet designs, cost-effective manufacturing, and reliable delivery.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 30\n                  }, this), \" Innovative Designs\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 30\n                  }, this), \" Competitive Pricing\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 30\n                  }, this), \" On-time Delivery\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      style: {\n        background: '#0056b3',\n        color: '#fff'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            color: '#fff',\n            marginBottom: 24\n          },\n          children: \"Ready to Start Your Project?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          style: {\n            fontSize: 16,\n            marginBottom: 32,\n            color: '#fff'\n          },\n          children: \"Contact us today to discuss your helmet manufacturing needs and discover how we can help bring your ideas to life.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"large\",\n          style: {\n            background: '#fff',\n            color: '#0056b3'\n          },\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            children: \"Contact Us Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "Link", "Typography", "<PERSON><PERSON>", "Row", "Col", "Card", "Space", "RightOutlined", "CheckCircleOutlined", "products", "services", "getImagePath", "VideoPlayer", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "Meta", "HomePage", "featuredProducts", "slice", "featuredServices", "children", "className", "style", "backgroundImage", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "size", "to", "gutter", "align", "xs", "md", "src", "level", "fontSize", "marginBottom", "textAlign", "map", "product", "sm", "lg", "hoverable", "cover", "alt", "name", "image", "height", "objectFit", "title", "description", "length", "substring", "marginTop", "id", "max<PERSON><PERSON><PERSON>", "margin", "service", "direction", "marginRight", "background", "_c", "$RefreshReg$"], "sources": ["C:/customized/ShouChuang Sporting/建站资料收集（最新版）/shouchuang-website/src/pages/Home.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Typography, Button, Row, Col, Card, Carousel, Space, Divider } from 'antd';\nimport { RightOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport { products, services } from '../models/mockData';\nimport { getImagePath } from '../utils/imageUtils';\nimport VideoPlayer from '../components/VideoPlayer';\n\nconst { Title, Paragraph } = Typography;\nconst { Meta } = Card;\n\nconst HomePage: React.FC = () => {\n  // Select featured products (first 4)\n  const featuredProducts = products.slice(0, 4);\n\n  // Select featured services (first 3)\n  const featuredServices = services.slice(0, 3);\n\n  return (\n    <div>\n      {/* Hero Section */}\n      <div\n        className=\"hero-section\"\n        style={{\n          backgroundImage: `url(${getImagePath('/images/hero-bg.jpg')})`,\n        }}\n      >\n        <div className=\"hero-content\">\n          <Title className=\"hero-title\" style={{ color: '#fff' }}>\n            XLL Sporting Goods Co., Ltd.\n          </Title>\n          {/* <Title level={2} className=\"hero-subtitle\" style={{ color: '#fff' }}>\n            ShouChuang Sporting Goods Co., Ltd.\n          </Title> */}\n          <Paragraph className=\"hero-description\" style={{ color: '#fff' }}>\n            Professional OEM/ODM Helmet Manufacturer\n          </Paragraph>\n          <Space>\n            <Button type=\"primary\" size=\"large\">\n              <Link to=\"/products\">Explore Our Products</Link>\n            </Button>\n            <Button size=\"large\">\n              <Link to=\"/contact\">Contact Us</Link>\n            </Button>\n          </Space>\n        </div>\n      </div>\n\n      {/* About Section */}\n      <section className=\"section\">\n        <div className=\"container\">\n          <Row gutter={[48, 24]} align=\"middle\">\n            <Col xs={24} md={12}>\n              <VideoPlayer\n                src=\"/video/20250714092754.mp4\"\n                // poster=\"/images/about-img.jpg\"\n              />\n            </Col>\n            <Col xs={24} md={12}>\n              <Title level={2}>About XLL</Title>\n              <Paragraph style={{ fontSize: 16, marginBottom: 16 }}>\n                XLL Sporting Goods Co., Ltd. is a professional helmet manufacturer specializing in the design, development, and production of high-quality helmets for various sports and activities.\n              </Paragraph>\n              <Paragraph style={{ fontSize: 16, marginBottom: 16 }}>\n                With our state-of-the-art manufacturing facility and experienced R&D team, we provide exceptional OEM and ODM services to clients worldwide.\n              </Paragraph>\n              <Paragraph style={{ fontSize: 16, marginBottom: 24 }}>\n                Our commitment to quality, innovation, and customer satisfaction has made us a trusted partner for sports equipment brands globally.\n              </Paragraph>\n              <Button type=\"primary\" size=\"large\">\n                <Link to=\"/about\">Learn More About Us <RightOutlined /></Link>\n              </Button>\n            </Col>\n          </Row>\n        </div>\n      </section>\n\n      {/* Products Section */}\n      <section className=\"section section-dark\">\n        <div className=\"container\">\n          <Title level={2} style={{ textAlign: 'center', marginBottom: 48 }}>Our Products</Title>\n          <Row gutter={[24, 24]}>\n            {featuredProducts.map(product => (\n              <Col xs={24} sm={12} lg={6} key={product.id}>\n                <Card\n                  hoverable\n                  className=\"product-card\"\n                  cover={<img alt={product.name} src={getImagePath(product.image)} style={{ height: 200, objectFit: 'cover' }} />}\n                >\n                  <Meta\n                    title={product.name}\n                    description={product.description.length > 100 ? `${product.description.substring(0, 100)}...` : product.description}\n                  />\n                  <div style={{ marginTop: 16 }}>\n                    <Button type=\"primary\">\n                      <Link to={`/products#${product.id}`}>View Details</Link>\n                    </Button>\n                  </div>\n                </Card>\n              </Col>\n            ))}\n          </Row>\n          <div style={{ textAlign: 'center', marginTop: 40 }}>\n            <Button type=\"primary\" size=\"large\">\n              <Link to=\"/products\">View All Products <RightOutlined /></Link>\n            </Button>\n          </div>\n        </div>\n      </section>\n\n      {/* Services Section */}\n      <section className=\"section\">\n        <div className=\"container\">\n          <Title level={2} style={{ textAlign: 'center', marginBottom: 16 }}>OEM & ODM Services</Title>\n          <Paragraph style={{ textAlign: 'center', fontSize: 16, maxWidth: 800, margin: '0 auto 48px' }}>\n            We provide comprehensive OEM and ODM services for helmet manufacturing, offering customized solutions to meet your specific requirements.\n          </Paragraph>\n          <Row gutter={[24, 24]}>\n            {featuredServices.map(service => (\n              <Col xs={24} md={8} key={service.id}>\n                <Card className=\"service-card\" style={{ height: '100%' }}>\n                  <Title level={4}>{service.title}</Title>\n                  <Paragraph>{service.description}</Paragraph>\n                </Card>\n              </Col>\n            ))}\n          </Row>\n          <div style={{ textAlign: 'center', marginTop: 40 }}>\n            <Button type=\"primary\" size=\"large\">\n              <Link to=\"/services\">Learn More About Our Services <RightOutlined /></Link>\n            </Button>\n          </div>\n        </div>\n      </section>\n\n      {/* Why Choose Us Section */}\n      <section className=\"section section-dark\">\n        <div className=\"container\">\n          <Title level={2} style={{ textAlign: 'center', marginBottom: 48 }}>Why Choose Us</Title>\n          <Row gutter={[24, 24]}>\n            <Col xs={24} md={8}>\n              <Card style={{ height: '100%' }}>\n                <Title level={4}>Quality Assurance</Title>\n                <Paragraph>\n                  All our helmets undergo rigorous testing to meet or exceed international safety standards, ensuring the highest quality products for your brand.\n                </Paragraph>\n                <Space direction=\"vertical\">\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> CE Certified</Paragraph>\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> ASTM/SEI Certified</Paragraph>\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> DOT/ECE Certified</Paragraph>\n                </Space>\n              </Card>\n            </Col>\n            <Col xs={24} md={8}>\n              <Card style={{ height: '100%' }}>\n                <Title level={4}>Customization Options</Title>\n                <Paragraph>\n                  We offer extensive customization options for all our helmet models, allowing you to create unique products that align with your brand identity.\n                </Paragraph>\n                <Space direction=\"vertical\">\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Custom Colors & Graphics</Paragraph>\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Custom Packaging</Paragraph>\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Custom Features & Accessories</Paragraph>\n                </Space>\n              </Card>\n            </Col>\n            <Col xs={24} md={8}>\n              <Card style={{ height: '100%' }}>\n                <Title level={4}>Competitive Advantage</Title>\n                <Paragraph>\n                  Partner with us to gain a competitive edge in the market with innovative helmet designs, cost-effective manufacturing, and reliable delivery.\n                </Paragraph>\n                <Space direction=\"vertical\">\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Innovative Designs</Paragraph>\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Competitive Pricing</Paragraph>\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> On-time Delivery</Paragraph>\n                </Space>\n              </Card>\n            </Col>\n          </Row>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"section\" style={{ background: '#0056b3', color: '#fff' }}>\n        <div className=\"container\" style={{ textAlign: 'center' }}>\n          <Title level={2} style={{ color: '#fff', marginBottom: 24 }}>Ready to Start Your Project?</Title>\n          <Paragraph style={{ fontSize: 16, marginBottom: 32, color: '#fff' }}>\n            Contact us today to discuss your helmet manufacturing needs and discover how we can help bring your ideas to life.\n          </Paragraph>\n          <Button size=\"large\" style={{ background: '#fff', color: '#0056b3' }}>\n            <Link to=\"/contact\">Contact Us Now</Link>\n          </Button>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAYC,KAAK,QAAiB,MAAM;AACnF,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,mBAAmB;AACtE,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAOC,WAAW,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAM;EAAEC,KAAK;EAAEC;AAAU,CAAC,GAAGf,UAAU;AACvC,MAAM;EAAEgB;AAAK,CAAC,GAAGZ,IAAI;AAErB,MAAMa,QAAkB,GAAGA,CAAA,KAAM;EAC/B;EACA,MAAMC,gBAAgB,GAAGV,QAAQ,CAACW,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;EAE7C;EACA,MAAMC,gBAAgB,GAAGX,QAAQ,CAACU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAE7C,oBACEN,OAAA;IAAAQ,QAAA,gBAEER,OAAA;MACES,SAAS,EAAC,cAAc;MACxBC,KAAK,EAAE;QACLC,eAAe,EAAE,OAAOd,YAAY,CAAC,qBAAqB,CAAC;MAC7D,CAAE;MAAAW,QAAA,eAEFR,OAAA;QAAKS,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3BR,OAAA,CAACC,KAAK;UAACQ,SAAS,EAAC,YAAY;UAACC,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAExD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAIRhB,OAAA,CAACE,SAAS;UAACO,SAAS,EAAC,kBAAkB;UAACC,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAElE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZhB,OAAA,CAACR,KAAK;UAAAgB,QAAA,gBACJR,OAAA,CAACZ,MAAM;YAAC6B,IAAI,EAAC,SAAS;YAACC,IAAI,EAAC,OAAO;YAAAV,QAAA,eACjCR,OAAA,CAACd,IAAI;cAACiC,EAAE,EAAC,WAAW;cAAAX,QAAA,EAAC;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACThB,OAAA,CAACZ,MAAM;YAAC8B,IAAI,EAAC,OAAO;YAAAV,QAAA,eAClBR,OAAA,CAACd,IAAI;cAACiC,EAAE,EAAC,UAAU;cAAAX,QAAA,EAAC;YAAU;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhB,OAAA;MAASS,SAAS,EAAC,SAAS;MAAAD,QAAA,eAC1BR,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBR,OAAA,CAACX,GAAG;UAAC+B,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACC,KAAK,EAAC,QAAQ;UAAAb,QAAA,gBACnCR,OAAA,CAACV,GAAG;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAf,QAAA,eAClBR,OAAA,CAACF,WAAW;cACV0B,GAAG,EAAC;cACJ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhB,OAAA,CAACV,GAAG;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAf,QAAA,gBAClBR,OAAA,CAACC,KAAK;cAACwB,KAAK,EAAE,CAAE;cAAAjB,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClChB,OAAA,CAACE,SAAS;cAACQ,KAAK,EAAE;gBAAEgB,QAAQ,EAAE,EAAE;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAAnB,QAAA,EAAC;YAEtD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACZhB,OAAA,CAACE,SAAS;cAACQ,KAAK,EAAE;gBAAEgB,QAAQ,EAAE,EAAE;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAAnB,QAAA,EAAC;YAEtD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACZhB,OAAA,CAACE,SAAS;cAACQ,KAAK,EAAE;gBAAEgB,QAAQ,EAAE,EAAE;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAAnB,QAAA,EAAC;YAEtD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACZhB,OAAA,CAACZ,MAAM;cAAC6B,IAAI,EAAC,SAAS;cAACC,IAAI,EAAC,OAAO;cAAAV,QAAA,eACjCR,OAAA,CAACd,IAAI;gBAACiC,EAAE,EAAC,QAAQ;gBAAAX,QAAA,GAAC,sBAAoB,eAAAR,OAAA,CAACP,aAAa;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhB,OAAA;MAASS,SAAS,EAAC,sBAAsB;MAAAD,QAAA,eACvCR,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxBR,OAAA,CAACC,KAAK;UAACwB,KAAK,EAAE,CAAE;UAACf,KAAK,EAAE;YAAEkB,SAAS,EAAE,QAAQ;YAAED,YAAY,EAAE;UAAG,CAAE;UAAAnB,QAAA,EAAC;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvFhB,OAAA,CAACX,GAAG;UAAC+B,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAZ,QAAA,EACnBH,gBAAgB,CAACwB,GAAG,CAACC,OAAO,iBAC3B9B,OAAA,CAACV,GAAG;YAACgC,EAAE,EAAE,EAAG;YAACS,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACzBR,OAAA,CAACT,IAAI;cACH0C,SAAS;cACTxB,SAAS,EAAC,cAAc;cACxByB,KAAK,eAAElC,OAAA;gBAAKmC,GAAG,EAAEL,OAAO,CAACM,IAAK;gBAACZ,GAAG,EAAE3B,YAAY,CAACiC,OAAO,CAACO,KAAK,CAAE;gBAAC3B,KAAK,EAAE;kBAAE4B,MAAM,EAAE,GAAG;kBAAEC,SAAS,EAAE;gBAAQ;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAR,QAAA,gBAEhHR,OAAA,CAACG,IAAI;gBACHqC,KAAK,EAAEV,OAAO,CAACM,IAAK;gBACpBK,WAAW,EAAEX,OAAO,CAACW,WAAW,CAACC,MAAM,GAAG,GAAG,GAAG,GAAGZ,OAAO,CAACW,WAAW,CAACE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GAAGb,OAAO,CAACW;cAAY;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrH,CAAC,eACFhB,OAAA;gBAAKU,KAAK,EAAE;kBAAEkC,SAAS,EAAE;gBAAG,CAAE;gBAAApC,QAAA,eAC5BR,OAAA,CAACZ,MAAM;kBAAC6B,IAAI,EAAC,SAAS;kBAAAT,QAAA,eACpBR,OAAA,CAACd,IAAI;oBAACiC,EAAE,EAAE,aAAaW,OAAO,CAACe,EAAE,EAAG;oBAAArC,QAAA,EAAC;kBAAY;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC,GAfwBc,OAAO,CAACe,EAAE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBtC,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhB,OAAA;UAAKU,KAAK,EAAE;YAAEkB,SAAS,EAAE,QAAQ;YAAEgB,SAAS,EAAE;UAAG,CAAE;UAAApC,QAAA,eACjDR,OAAA,CAACZ,MAAM;YAAC6B,IAAI,EAAC,SAAS;YAACC,IAAI,EAAC,OAAO;YAAAV,QAAA,eACjCR,OAAA,CAACd,IAAI;cAACiC,EAAE,EAAC,WAAW;cAAAX,QAAA,GAAC,oBAAkB,eAAAR,OAAA,CAACP,aAAa;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhB,OAAA;MAASS,SAAS,EAAC,SAAS;MAAAD,QAAA,eAC1BR,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxBR,OAAA,CAACC,KAAK;UAACwB,KAAK,EAAE,CAAE;UAACf,KAAK,EAAE;YAAEkB,SAAS,EAAE,QAAQ;YAAED,YAAY,EAAE;UAAG,CAAE;UAAAnB,QAAA,EAAC;QAAkB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7FhB,OAAA,CAACE,SAAS;UAACQ,KAAK,EAAE;YAAEkB,SAAS,EAAE,QAAQ;YAAEF,QAAQ,EAAE,EAAE;YAAEoB,QAAQ,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAc,CAAE;UAAAvC,QAAA,EAAC;QAE/F;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZhB,OAAA,CAACX,GAAG;UAAC+B,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAZ,QAAA,EACnBD,gBAAgB,CAACsB,GAAG,CAACmB,OAAO,iBAC3BhD,OAAA,CAACV,GAAG;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAf,QAAA,eACjBR,OAAA,CAACT,IAAI;cAACkB,SAAS,EAAC,cAAc;cAACC,KAAK,EAAE;gBAAE4B,MAAM,EAAE;cAAO,CAAE;cAAA9B,QAAA,gBACvDR,OAAA,CAACC,KAAK;gBAACwB,KAAK,EAAE,CAAE;gBAAAjB,QAAA,EAAEwC,OAAO,CAACR;cAAK;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxChB,OAAA,CAACE,SAAS;gBAAAM,QAAA,EAAEwC,OAAO,CAACP;cAAW;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC,GAJgBgC,OAAO,CAACH,EAAE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAK9B,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhB,OAAA;UAAKU,KAAK,EAAE;YAAEkB,SAAS,EAAE,QAAQ;YAAEgB,SAAS,EAAE;UAAG,CAAE;UAAApC,QAAA,eACjDR,OAAA,CAACZ,MAAM;YAAC6B,IAAI,EAAC,SAAS;YAACC,IAAI,EAAC,OAAO;YAAAV,QAAA,eACjCR,OAAA,CAACd,IAAI;cAACiC,EAAE,EAAC,WAAW;cAAAX,QAAA,GAAC,gCAA8B,eAAAR,OAAA,CAACP,aAAa;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhB,OAAA;MAASS,SAAS,EAAC,sBAAsB;MAAAD,QAAA,eACvCR,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxBR,OAAA,CAACC,KAAK;UAACwB,KAAK,EAAE,CAAE;UAACf,KAAK,EAAE;YAAEkB,SAAS,EAAE,QAAQ;YAAED,YAAY,EAAE;UAAG,CAAE;UAAAnB,QAAA,EAAC;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxFhB,OAAA,CAACX,GAAG;UAAC+B,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAZ,QAAA,gBACpBR,OAAA,CAACV,GAAG;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAf,QAAA,eACjBR,OAAA,CAACT,IAAI;cAACmB,KAAK,EAAE;gBAAE4B,MAAM,EAAE;cAAO,CAAE;cAAA9B,QAAA,gBAC9BR,OAAA,CAACC,KAAK;gBAACwB,KAAK,EAAE,CAAE;gBAAAjB,QAAA,EAAC;cAAiB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1ChB,OAAA,CAACE,SAAS;gBAAAM,QAAA,EAAC;cAEX;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACZhB,OAAA,CAACR,KAAK;gBAACyD,SAAS,EAAC,UAAU;gBAAAzC,QAAA,gBACzBR,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACN,mBAAmB;oBAACgB,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAEsC,WAAW,EAAE;oBAAE;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAAa;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACxGhB,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACN,mBAAmB;oBAACgB,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAEsC,WAAW,EAAE;oBAAE;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uBAAmB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9GhB,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACN,mBAAmB;oBAACgB,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAEsC,WAAW,EAAE;oBAAE;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAAkB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNhB,OAAA,CAACV,GAAG;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAf,QAAA,eACjBR,OAAA,CAACT,IAAI;cAACmB,KAAK,EAAE;gBAAE4B,MAAM,EAAE;cAAO,CAAE;cAAA9B,QAAA,gBAC9BR,OAAA,CAACC,KAAK;gBAACwB,KAAK,EAAE,CAAE;gBAAAjB,QAAA,EAAC;cAAqB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9ChB,OAAA,CAACE,SAAS;gBAAAM,QAAA,EAAC;cAEX;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACZhB,OAAA,CAACR,KAAK;gBAACyD,SAAS,EAAC,UAAU;gBAAAzC,QAAA,gBACzBR,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACN,mBAAmB;oBAACgB,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAEsC,WAAW,EAAE;oBAAE;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,6BAAyB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpHhB,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACN,mBAAmB;oBAACgB,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAEsC,WAAW,EAAE;oBAAE;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qBAAiB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5GhB,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACN,mBAAmB;oBAACgB,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAEsC,WAAW,EAAE;oBAAE;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kCAA8B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNhB,OAAA,CAACV,GAAG;YAACgC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAf,QAAA,eACjBR,OAAA,CAACT,IAAI;cAACmB,KAAK,EAAE;gBAAE4B,MAAM,EAAE;cAAO,CAAE;cAAA9B,QAAA,gBAC9BR,OAAA,CAACC,KAAK;gBAACwB,KAAK,EAAE,CAAE;gBAAAjB,QAAA,EAAC;cAAqB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9ChB,OAAA,CAACE,SAAS;gBAAAM,QAAA,EAAC;cAEX;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACZhB,OAAA,CAACR,KAAK;gBAACyD,SAAS,EAAC,UAAU;gBAAAzC,QAAA,gBACzBR,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACN,mBAAmB;oBAACgB,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAEsC,WAAW,EAAE;oBAAE;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uBAAmB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9GhB,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACN,mBAAmB;oBAACgB,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAEsC,WAAW,EAAE;oBAAE;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,wBAAoB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC/GhB,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACN,mBAAmB;oBAACgB,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAEsC,WAAW,EAAE;oBAAE;kBAAE;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qBAAiB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhB,OAAA;MAASS,SAAS,EAAC,SAAS;MAACC,KAAK,EAAE;QAAEyC,UAAU,EAAE,SAAS;QAAEvC,KAAK,EAAE;MAAO,CAAE;MAAAJ,QAAA,eAC3ER,OAAA;QAAKS,SAAS,EAAC,WAAW;QAACC,KAAK,EAAE;UAAEkB,SAAS,EAAE;QAAS,CAAE;QAAApB,QAAA,gBACxDR,OAAA,CAACC,KAAK;UAACwB,KAAK,EAAE,CAAE;UAACf,KAAK,EAAE;YAAEE,KAAK,EAAE,MAAM;YAAEe,YAAY,EAAE;UAAG,CAAE;UAAAnB,QAAA,EAAC;QAA4B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjGhB,OAAA,CAACE,SAAS;UAACQ,KAAK,EAAE;YAAEgB,QAAQ,EAAE,EAAE;YAAEC,YAAY,EAAE,EAAE;YAAEf,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAErE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZhB,OAAA,CAACZ,MAAM;UAAC8B,IAAI,EAAC,OAAO;UAACR,KAAK,EAAE;YAAEyC,UAAU,EAAE,MAAM;YAAEvC,KAAK,EAAE;UAAU,CAAE;UAAAJ,QAAA,eACnER,OAAA,CAACd,IAAI;YAACiC,EAAE,EAAC,UAAU;YAAAX,QAAA,EAAC;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACoC,EAAA,GA1LIhD,QAAkB;AA4LxB,eAAeA,QAAQ;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}