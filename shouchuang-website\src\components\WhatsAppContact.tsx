import React, { useState } from 'react';
import { Modal, Typography } from 'antd';
import { getImagePath } from '../utils/imageUtils';

const { Text } = Typography;

interface WhatsAppContactProps {
  imageSrc: string;
  altText?: string;
  description?: string;
  className?: string;
  style?: React.CSSProperties;
}

const WhatsAppContact: React.FC<WhatsAppContactProps> = ({
  imageSrc,
  altText = "WhatsApp Contact",
  description = "Contact us via WhatsApp:",
  className = "",
  style = {}
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  return (
    <>
      <div style={style}>
        {description && (
          <Text style={{ 
            color: 'rgba(255,255,255,0.65)', 
            fontSize: '12px', 
            display: 'block', 
            marginBottom: 8 
          }}>
            {description}
          </Text>
        )}
        <img
          src={getImagePath(imageSrc)}
          alt={altText}
          className={`whatsapp-image ${className}`}
          onClick={showModal}
          title="Click to view larger image"
        />
      </div>

      <Modal
        title="WhatsApp Contact"
        open={isModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={400}
        centered
      >
        <div style={{ textAlign: 'center' }}>
          <img
            src={getImagePath(imageSrc)}
            alt={altText}
            style={{ 
              width: '100%', 
              height: 'auto', 
              borderRadius: 8,
              marginBottom: 16
            }}
          />
          <Text style={{ fontSize: '14px', color: '#666' }}>
            Scan the QR code above to contact us directly via WhatsApp
          </Text>
        </div>
      </Modal>
    </>
  );
};

export default WhatsAppContact;
