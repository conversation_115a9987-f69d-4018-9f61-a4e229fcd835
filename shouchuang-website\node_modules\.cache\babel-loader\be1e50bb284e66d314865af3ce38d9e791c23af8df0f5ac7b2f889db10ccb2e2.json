{"ast": null, "code": "var _jsxFileName = \"C:\\\\customized\\\\ShouChuang Sporting\\\\\\u5EFA\\u7AD9\\u8D44\\u6599\\u6536\\u96C6\\uFF08\\u6700\\u65B0\\u7248\\uFF09\\\\shouchuang-website\\\\src\\\\pages\\\\Products.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Typography, Row, Col, Card, Button, Tabs, Tag, List, Divider, Space, Input } from 'antd';\nimport { SearchOutlined, CheckOutlined } from '@ant-design/icons';\nimport { products } from '../models/mockData';\nimport { getImagePath } from '../utils/imageUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Meta\n} = Card;\nconst {\n  Search\n} = Input;\nconst ProductsPage = () => {\n  _s();\n  const [activeCategory, setActiveCategory] = useState('All');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [filteredProducts, setFilteredProducts] = useState(products);\n\n  // Get unique categories\n  const categories = ['All', ...Array.from(new Set(products.map(product => product.category)))];\n\n  // Filter products based on category and search query\n  useEffect(() => {\n    let filtered = products;\n\n    // Filter by category\n    if (activeCategory !== 'All') {\n      filtered = filtered.filter(product => product.category === activeCategory);\n    }\n\n    // Filter by search query\n    if (searchQuery) {\n      const query = searchQuery.toLowerCase();\n      filtered = filtered.filter(product => product.name.toLowerCase().includes(query) || product.description.toLowerCase().includes(query));\n    }\n    setFilteredProducts(filtered);\n  }, [activeCategory, searchQuery]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: 48\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"Our Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n        style: {\n          fontSize: 16,\n          maxWidth: 800,\n          margin: '0 auto'\n        },\n        children: \"Explore our wide range of high-quality helmets designed for various sports and activities. All our helmets are manufactured to meet or exceed international safety standards.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 32\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Search, {\n            placeholder: \"Search products\",\n            allowClear: true,\n            enterButton: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 28\n            }, this),\n            size: \"large\",\n            onSearch: value => setSearchQuery(value),\n            onChange: e => setSearchQuery(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 16,\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            activeKey: activeCategory,\n            onChange: setActiveCategory,\n            type: \"card\",\n            size: \"large\",\n            style: {\n              marginBottom: 0\n            },\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(TabPane, {\n              tab: category\n            }, category, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [24, 32],\n      children: filteredProducts.length > 0 ? filteredProducts.map(product => /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 8,\n        id: product.id,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          hoverable: true,\n          className: \"product-card\",\n          cover: /*#__PURE__*/_jsxDEV(\"img\", {\n            alt: product.name,\n            src: getImagePath(product.image)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 24\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Meta, {\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [product.name, /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"#0056b3\",\n                children: product.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 21\n            }, this),\n            description: product.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 5,\n            children: \"Key Features:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            size: \"small\",\n            dataSource: product.features.slice(0, 4),\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: [/*#__PURE__*/_jsxDEV(CheckOutlined, {\n                style: {\n                  color: '#52c41a',\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 23\n              }, this), \" \", item]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 17\n          }, this), product.features.length > 4 && /*#__PURE__*/_jsxDEV(Paragraph, {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              style: {\n                padding: 0\n              },\n              children: [\"+\", product.features.length - 4, \" more features\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              block: true,\n              children: \"Request Quote\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 15\n        }, this)\n      }, product.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 13\n      }, this)) : /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        style: {\n          textAlign: 'center',\n          padding: '40px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"No products found matching your criteria.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          children: \"Please try a different search term or category.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: () => {\n            setActiveCategory('All');\n            setSearchQuery('');\n          },\n          children: \"Reset Filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      style: {\n        marginTop: 64,\n        background: '#f5f5f5',\n        padding: 32,\n        borderRadius: 8\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [48, 24],\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/images/products/custom-helmet.jpg\",\n            alt: \"Custom Helmet\",\n            style: {\n              width: '100%',\n              borderRadius: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            children: \"Need a Custom Helmet Solution?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            style: {\n              fontSize: 16,\n              marginBottom: 16\n            },\n            children: \"We specialize in OEM and ODM helmet manufacturing, offering customized solutions to meet your specific requirements.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            style: {\n              fontSize: 16,\n              marginBottom: 16\n            },\n            children: \"Whether you need a unique design, special features, or custom branding, our experienced team can bring your vision to life.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            style: {\n              fontSize: 16,\n              marginBottom: 24\n            },\n            children: \"Contact us today to discuss your custom helmet project and discover how we can help you create the perfect helmet for your brand.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            children: \"Contact Us for Custom Solutions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      style: {\n        marginTop: 64\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          textAlign: 'center',\n          marginBottom: 32\n        },\n        children: \"Safety Standards & Certifications\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n        style: {\n          textAlign: 'center',\n          fontSize: 16,\n          maxWidth: 800,\n          margin: '0 auto 32px'\n        },\n        children: \"All our helmets are designed and manufactured to meet or exceed international safety standards, ensuring the highest level of protection for users.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        justify: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/images/certifications/ce.png\",\n            alt: \"CE Certification\",\n            style: {\n              height: 80,\n              marginBottom: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            strong: true,\n            children: \"CE Certified\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/images/certifications/astm.png\",\n            alt: \"ASTM Certification\",\n            style: {\n              height: 80,\n              marginBottom: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            strong: true,\n            children: \"ASTM Certified\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/images/certifications/iso.png\",\n            alt: \"ISO Certification\",\n            style: {\n              height: 80,\n              marginBottom: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            strong: true,\n            children: \"ISO 9001\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/images/certifications/dot.png\",\n            alt: \"DOT Certification\",\n            style: {\n              height: 80,\n              marginBottom: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            strong: true,\n            children: \"DOT/ECE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductsPage, \"22Y0V4slBQdiqmiW4ENWQGqc+Hg=\");\n_c = ProductsPage;\nexport default ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Typography", "Row", "Col", "Card", "<PERSON><PERSON>", "Tabs", "Tag", "List", "Divider", "Space", "Input", "SearchOutlined", "CheckOutlined", "products", "getImagePath", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "TabPane", "Meta", "Search", "ProductsPage", "_s", "activeCategory", "setActiveCategory", "searchQuery", "setSearch<PERSON>uery", "filteredProducts", "setFilteredProducts", "categories", "Array", "from", "Set", "map", "product", "category", "filtered", "filter", "query", "toLowerCase", "name", "includes", "description", "className", "children", "style", "textAlign", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "max<PERSON><PERSON><PERSON>", "margin", "gutter", "align", "xs", "md", "placeholder", "allowClear", "enterButton", "size", "onSearch", "value", "onChange", "e", "target", "active<PERSON><PERSON>", "type", "tab", "length", "sm", "lg", "id", "hoverable", "cover", "alt", "src", "image", "title", "color", "level", "dataSource", "features", "slice", "renderItem", "item", "<PERSON><PERSON>", "marginRight", "marginTop", "padding", "block", "span", "onClick", "background", "borderRadius", "width", "justify", "height", "strong", "_c", "$RefreshReg$"], "sources": ["C:/customized/ShouChuang Sporting/建站资料收集（最新版）/shouchuang-website/src/pages/Products.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Typo<PERSON>, <PERSON>, Col, Card, Button, Tabs, Tag, List, Divider, Space, Input } from 'antd';\nimport { SearchOutlined, CheckOutlined } from '@ant-design/icons';\nimport { products } from '../models/mockData';\nimport { Product } from '../models/types';\nimport { getImagePath } from '../utils/imageUtils';\n\nconst { Title, Paragraph } = Typography;\nconst { TabPane } = Tabs;\nconst { Meta } = Card;\nconst { Search } = Input;\n\nconst ProductsPage: React.FC = () => {\n  const [activeCategory, setActiveCategory] = useState<string>('All');\n  const [searchQuery, setSearchQuery] = useState<string>('');\n  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products);\n  \n  // Get unique categories\n  const categories = ['All', ...Array.from(new Set(products.map(product => product.category)))];\n  \n  // Filter products based on category and search query\n  useEffect(() => {\n    let filtered = products;\n    \n    // Filter by category\n    if (activeCategory !== 'All') {\n      filtered = filtered.filter(product => product.category === activeCategory);\n    }\n    \n    // Filter by search query\n    if (searchQuery) {\n      const query = searchQuery.toLowerCase();\n      filtered = filtered.filter(\n        product => \n          product.name.toLowerCase().includes(query) || \n          product.description.toLowerCase().includes(query)\n      );\n    }\n    \n    setFilteredProducts(filtered);\n  }, [activeCategory, searchQuery]);\n\n  return (\n    <div className=\"page-container\">\n      {/* Page Header */}\n      <div style={{ textAlign: 'center', marginBottom: 48 }}>\n        <Title>Our Products</Title>\n        <Paragraph style={{ fontSize: 16, maxWidth: 800, margin: '0 auto' }}>\n          Explore our wide range of high-quality helmets designed for various sports and activities.\n          All our helmets are manufactured to meet or exceed international safety standards.\n        </Paragraph>\n      </div>\n\n      {/* Search and Filter */}\n      <div style={{ marginBottom: 32 }}>\n        <Row gutter={[16, 16]} align=\"middle\">\n          <Col xs={24} md={8}>\n            <Search\n              placeholder=\"Search products\"\n              allowClear\n              enterButton={<SearchOutlined />}\n              size=\"large\"\n              onSearch={(value) => setSearchQuery(value)}\n              onChange={(e) => setSearchQuery(e.target.value)}\n            />\n          </Col>\n          <Col xs={24} md={16}>\n            <Tabs \n              activeKey={activeCategory} \n              onChange={setActiveCategory}\n              type=\"card\"\n              size=\"large\"\n              style={{ marginBottom: 0 }}\n            >\n              {categories.map(category => (\n                <TabPane tab={category} key={category} />\n              ))}\n            </Tabs>\n          </Col>\n        </Row>\n      </div>\n\n      {/* Products Grid */}\n      <Row gutter={[24, 32]}>\n        {filteredProducts.length > 0 ? (\n          filteredProducts.map(product => (\n            <Col xs={24} sm={12} lg={8} key={product.id} id={product.id}>\n              <Card\n                hoverable\n                className=\"product-card\"\n                cover={<img alt={product.name} src={getImagePath(product.image)} />}\n              >\n                <Meta \n                  title={\n                    <Space>\n                      {product.name}\n                      <Tag color=\"#0056b3\">{product.category}</Tag>\n                    </Space>\n                  } \n                  description={product.description} \n                />\n                <Divider />\n                <Title level={5}>Key Features:</Title>\n                <List\n                  size=\"small\"\n                  dataSource={product.features.slice(0, 4)}\n                  renderItem={item => (\n                    <List.Item>\n                      <CheckOutlined style={{ color: '#52c41a', marginRight: 8 }} /> {item}\n                    </List.Item>\n                  )}\n                />\n                {product.features.length > 4 && (\n                  <Paragraph style={{ marginTop: 8 }}>\n                    <Button type=\"link\" style={{ padding: 0 }}>\n                      +{product.features.length - 4} more features\n                    </Button>\n                  </Paragraph>\n                )}\n                <div style={{ marginTop: 16 }}>\n                  <Button type=\"primary\" block>\n                    Request Quote\n                  </Button>\n                </div>\n              </Card>\n            </Col>\n          ))\n        ) : (\n          <Col span={24} style={{ textAlign: 'center', padding: '40px 0' }}>\n            <Title level={4}>No products found matching your criteria.</Title>\n            <Paragraph>\n              Please try a different search term or category.\n            </Paragraph>\n            <Button \n              type=\"primary\" \n              onClick={() => {\n                setActiveCategory('All');\n                setSearchQuery('');\n              }}\n            >\n              Reset Filters\n            </Button>\n          </Col>\n        )}\n      </Row>\n\n      {/* Custom Helmet Section */}\n      <section className=\"section\" style={{ marginTop: 64, background: '#f5f5f5', padding: 32, borderRadius: 8 }}>\n        <Row gutter={[48, 24]} align=\"middle\">\n          <Col xs={24} md={12}>\n            <img \n              src=\"/images/products/custom-helmet.jpg\" \n              alt=\"Custom Helmet\" \n              style={{ width: '100%', borderRadius: 8 }} \n            />\n          </Col>\n          <Col xs={24} md={12}>\n            <Title level={2}>Need a Custom Helmet Solution?</Title>\n            <Paragraph style={{ fontSize: 16, marginBottom: 16 }}>\n              We specialize in OEM and ODM helmet manufacturing, offering customized solutions to meet your specific requirements.\n            </Paragraph>\n            <Paragraph style={{ fontSize: 16, marginBottom: 16 }}>\n              Whether you need a unique design, special features, or custom branding, our experienced team can bring your vision to life.\n            </Paragraph>\n            <Paragraph style={{ fontSize: 16, marginBottom: 24 }}>\n              Contact us today to discuss your custom helmet project and discover how we can help you create the perfect helmet for your brand.\n            </Paragraph>\n            <Button type=\"primary\" size=\"large\">\n              Contact Us for Custom Solutions\n            </Button>\n          </Col>\n        </Row>\n      </section>\n\n      {/* Safety Standards Section */}\n      <section className=\"section\" style={{ marginTop: 64 }}>\n        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>\n          Safety Standards & Certifications\n        </Title>\n        <Paragraph style={{ textAlign: 'center', fontSize: 16, maxWidth: 800, margin: '0 auto 32px' }}>\n          All our helmets are designed and manufactured to meet or exceed international safety standards,\n          ensuring the highest level of protection for users.\n        </Paragraph>\n        <Row gutter={[24, 24]} justify=\"center\">\n          <Col xs={12} sm={6} md={4} style={{ textAlign: 'center' }}>\n            <img src=\"/images/certifications/ce.png\" alt=\"CE Certification\" style={{ height: 80, marginBottom: 8 }} />\n            <Paragraph strong>CE Certified</Paragraph>\n          </Col>\n          <Col xs={12} sm={6} md={4} style={{ textAlign: 'center' }}>\n            <img src=\"/images/certifications/astm.png\" alt=\"ASTM Certification\" style={{ height: 80, marginBottom: 8 }} />\n            <Paragraph strong>ASTM Certified</Paragraph>\n          </Col>\n          <Col xs={12} sm={6} md={4} style={{ textAlign: 'center' }}>\n            <img src=\"/images/certifications/iso.png\" alt=\"ISO Certification\" style={{ height: 80, marginBottom: 8 }} />\n            <Paragraph strong>ISO 9001</Paragraph>\n          </Col>\n          <Col xs={12} sm={6} md={4} style={{ textAlign: 'center' }}>\n            <img src=\"/images/certifications/dot.png\" alt=\"DOT Certification\" style={{ height: 80, marginBottom: 8 }} />\n            <Paragraph strong>DOT/ECE</Paragraph>\n          </Col>\n        </Row>\n      </section>\n    </div>\n  );\n};\n\nexport default ProductsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACjG,SAASC,cAAc,EAAEC,aAAa,QAAQ,mBAAmB;AACjE,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,SAASC,YAAY,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAM;EAAEC,KAAK;EAAEC;AAAU,CAAC,GAAGlB,UAAU;AACvC,MAAM;EAAEmB;AAAQ,CAAC,GAAGd,IAAI;AACxB,MAAM;EAAEe;AAAK,CAAC,GAAGjB,IAAI;AACrB,MAAM;EAAEkB;AAAO,CAAC,GAAGX,KAAK;AAExB,MAAMY,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAS,KAAK,CAAC;EACnE,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAYe,QAAQ,CAAC;;EAE7E;EACA,MAAMiB,UAAU,GAAG,CAAC,KAAK,EAAE,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACpB,QAAQ,CAACqB,GAAG,CAACC,OAAO,IAAIA,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE7F;EACArC,SAAS,CAAC,MAAM;IACd,IAAIsC,QAAQ,GAAGxB,QAAQ;;IAEvB;IACA,IAAIW,cAAc,KAAK,KAAK,EAAE;MAC5Ba,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACH,OAAO,IAAIA,OAAO,CAACC,QAAQ,KAAKZ,cAAc,CAAC;IAC5E;;IAEA;IACA,IAAIE,WAAW,EAAE;MACf,MAAMa,KAAK,GAAGb,WAAW,CAACc,WAAW,CAAC,CAAC;MACvCH,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CACxBH,OAAO,IACLA,OAAO,CAACM,IAAI,CAACD,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,KAAK,CAAC,IAC1CJ,OAAO,CAACQ,WAAW,CAACH,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,KAAK,CACpD,CAAC;IACH;IAEAV,mBAAmB,CAACQ,QAAQ,CAAC;EAC/B,CAAC,EAAE,CAACb,cAAc,EAAEE,WAAW,CAAC,CAAC;EAEjC,oBACEV,OAAA;IAAK4B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAE7B7B,OAAA;MAAK8B,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBACpD7B,OAAA,CAACC,KAAK;QAAA4B,QAAA,EAAC;MAAY;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC3BpC,OAAA,CAACE,SAAS;QAAC4B,KAAK,EAAE;UAAEO,QAAQ,EAAE,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAV,QAAA,EAAC;MAGrE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGNpC,OAAA;MAAK8B,KAAK,EAAE;QAAEE,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,eAC/B7B,OAAA,CAACf,GAAG;QAACuD,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACC,KAAK,EAAC,QAAQ;QAAAZ,QAAA,gBACnC7B,OAAA,CAACd,GAAG;UAACwD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eACjB7B,OAAA,CAACK,MAAM;YACLuC,WAAW,EAAC,iBAAiB;YAC7BC,UAAU;YACVC,WAAW,eAAE9C,OAAA,CAACL,cAAc;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCW,IAAI,EAAC,OAAO;YACZC,QAAQ,EAAGC,KAAK,IAAKtC,cAAc,CAACsC,KAAK,CAAE;YAC3CC,QAAQ,EAAGC,CAAC,IAAKxC,cAAc,CAACwC,CAAC,CAACC,MAAM,CAACH,KAAK;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNpC,OAAA,CAACd,GAAG;UAACwD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAClB7B,OAAA,CAACX,IAAI;YACHgE,SAAS,EAAE7C,cAAe;YAC1B0C,QAAQ,EAAEzC,iBAAkB;YAC5B6C,IAAI,EAAC,MAAM;YACXP,IAAI,EAAC,OAAO;YACZjB,KAAK,EAAE;cAAEE,YAAY,EAAE;YAAE,CAAE;YAAAH,QAAA,EAE1Bf,UAAU,CAACI,GAAG,CAACE,QAAQ,iBACtBpB,OAAA,CAACG,OAAO;cAACoD,GAAG,EAAEnC;YAAS,GAAMA,QAAQ;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpC,OAAA,CAACf,GAAG;MAACuD,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAX,QAAA,EACnBjB,gBAAgB,CAAC4C,MAAM,GAAG,CAAC,GAC1B5C,gBAAgB,CAACM,GAAG,CAACC,OAAO,iBAC1BnB,OAAA,CAACd,GAAG;QAACwD,EAAE,EAAE,EAAG;QAACe,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAkBC,EAAE,EAAExC,OAAO,CAACwC,EAAG;QAAA9B,QAAA,eAC1D7B,OAAA,CAACb,IAAI;UACHyE,SAAS;UACThC,SAAS,EAAC,cAAc;UACxBiC,KAAK,eAAE7D,OAAA;YAAK8D,GAAG,EAAE3C,OAAO,CAACM,IAAK;YAACsC,GAAG,EAAEjE,YAAY,CAACqB,OAAO,CAAC6C,KAAK;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAP,QAAA,gBAEpE7B,OAAA,CAACI,IAAI;YACH6D,KAAK,eACHjE,OAAA,CAACP,KAAK;cAAAoC,QAAA,GACHV,OAAO,CAACM,IAAI,eACbzB,OAAA,CAACV,GAAG;gBAAC4E,KAAK,EAAC,SAAS;gBAAArC,QAAA,EAAEV,OAAO,CAACC;cAAQ;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CACR;YACDT,WAAW,EAAER,OAAO,CAACQ;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACFpC,OAAA,CAACR,OAAO;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACXpC,OAAA,CAACC,KAAK;YAACkE,KAAK,EAAE,CAAE;YAAAtC,QAAA,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtCpC,OAAA,CAACT,IAAI;YACHwD,IAAI,EAAC,OAAO;YACZqB,UAAU,EAAEjD,OAAO,CAACkD,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAE;YACzCC,UAAU,EAAEC,IAAI,iBACdxE,OAAA,CAACT,IAAI,CAACkF,IAAI;cAAA5C,QAAA,gBACR7B,OAAA,CAACJ,aAAa;gBAACkC,KAAK,EAAE;kBAAEoC,KAAK,EAAE,SAAS;kBAAEQ,WAAW,EAAE;gBAAE;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,KAAC,EAACoC,IAAI;YAAA;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACDjB,OAAO,CAACkD,QAAQ,CAACb,MAAM,GAAG,CAAC,iBAC1BxD,OAAA,CAACE,SAAS;YAAC4B,KAAK,EAAE;cAAE6C,SAAS,EAAE;YAAE,CAAE;YAAA9C,QAAA,eACjC7B,OAAA,CAACZ,MAAM;cAACkE,IAAI,EAAC,MAAM;cAACxB,KAAK,EAAE;gBAAE8C,OAAO,EAAE;cAAE,CAAE;cAAA/C,QAAA,GAAC,GACxC,EAACV,OAAO,CAACkD,QAAQ,CAACb,MAAM,GAAG,CAAC,EAAC,gBAChC;YAAA;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACZ,eACDpC,OAAA;YAAK8B,KAAK,EAAE;cAAE6C,SAAS,EAAE;YAAG,CAAE;YAAA9C,QAAA,eAC5B7B,OAAA,CAACZ,MAAM;cAACkE,IAAI,EAAC,SAAS;cAACuB,KAAK;cAAAhD,QAAA,EAAC;YAE7B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC,GAtCwBjB,OAAO,CAACwC,EAAE;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuCtC,CACN,CAAC,gBAEFpC,OAAA,CAACd,GAAG;QAAC4F,IAAI,EAAE,EAAG;QAAChD,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAE6C,OAAO,EAAE;QAAS,CAAE;QAAA/C,QAAA,gBAC/D7B,OAAA,CAACC,KAAK;UAACkE,KAAK,EAAE,CAAE;UAAAtC,QAAA,EAAC;QAAyC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClEpC,OAAA,CAACE,SAAS;UAAA2B,QAAA,EAAC;QAEX;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZpC,OAAA,CAACZ,MAAM;UACLkE,IAAI,EAAC,SAAS;UACdyB,OAAO,EAAEA,CAAA,KAAM;YACbtE,iBAAiB,CAAC,KAAK,CAAC;YACxBE,cAAc,CAAC,EAAE,CAAC;UACpB,CAAE;UAAAkB,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNpC,OAAA;MAAS4B,SAAS,EAAC,SAAS;MAACE,KAAK,EAAE;QAAE6C,SAAS,EAAE,EAAE;QAAEK,UAAU,EAAE,SAAS;QAAEJ,OAAO,EAAE,EAAE;QAAEK,YAAY,EAAE;MAAE,CAAE;MAAApD,QAAA,eACzG7B,OAAA,CAACf,GAAG;QAACuD,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACC,KAAK,EAAC,QAAQ;QAAAZ,QAAA,gBACnC7B,OAAA,CAACd,GAAG;UAACwD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,eAClB7B,OAAA;YACE+D,GAAG,EAAC,oCAAoC;YACxCD,GAAG,EAAC,eAAe;YACnBhC,KAAK,EAAE;cAAEoD,KAAK,EAAE,MAAM;cAAED,YAAY,EAAE;YAAE;UAAE;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNpC,OAAA,CAACd,GAAG;UAACwD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAd,QAAA,gBAClB7B,OAAA,CAACC,KAAK;YAACkE,KAAK,EAAE,CAAE;YAAAtC,QAAA,EAAC;UAA8B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvDpC,OAAA,CAACE,SAAS;YAAC4B,KAAK,EAAE;cAAEO,QAAQ,EAAE,EAAE;cAAEL,YAAY,EAAE;YAAG,CAAE;YAAAH,QAAA,EAAC;UAEtD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZpC,OAAA,CAACE,SAAS;YAAC4B,KAAK,EAAE;cAAEO,QAAQ,EAAE,EAAE;cAAEL,YAAY,EAAE;YAAG,CAAE;YAAAH,QAAA,EAAC;UAEtD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZpC,OAAA,CAACE,SAAS;YAAC4B,KAAK,EAAE;cAAEO,QAAQ,EAAE,EAAE;cAAEL,YAAY,EAAE;YAAG,CAAE;YAAAH,QAAA,EAAC;UAEtD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZpC,OAAA,CAACZ,MAAM;YAACkE,IAAI,EAAC,SAAS;YAACP,IAAI,EAAC,OAAO;YAAAlB,QAAA,EAAC;UAEpC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVpC,OAAA;MAAS4B,SAAS,EAAC,SAAS;MAACE,KAAK,EAAE;QAAE6C,SAAS,EAAE;MAAG,CAAE;MAAA9C,QAAA,gBACpD7B,OAAA,CAACC,KAAK;QAACkE,KAAK,EAAE,CAAE;QAACrC,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAH,QAAA,EAAC;MAEnE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRpC,OAAA,CAACE,SAAS;QAAC4B,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEM,QAAQ,EAAE,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAc,CAAE;QAAAV,QAAA,EAAC;MAG/F;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZpC,OAAA,CAACf,GAAG;QAACuD,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAC2C,OAAO,EAAC,QAAQ;QAAAtD,QAAA,gBACrC7B,OAAA,CAACd,GAAG;UAACwD,EAAE,EAAE,EAAG;UAACe,EAAE,EAAE,CAAE;UAACd,EAAE,EAAE,CAAE;UAACb,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAF,QAAA,gBACxD7B,OAAA;YAAK+D,GAAG,EAAC,+BAA+B;YAACD,GAAG,EAAC,kBAAkB;YAAChC,KAAK,EAAE;cAAEsD,MAAM,EAAE,EAAE;cAAEpD,YAAY,EAAE;YAAE;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1GpC,OAAA,CAACE,SAAS;YAACmF,MAAM;YAAAxD,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNpC,OAAA,CAACd,GAAG;UAACwD,EAAE,EAAE,EAAG;UAACe,EAAE,EAAE,CAAE;UAACd,EAAE,EAAE,CAAE;UAACb,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAF,QAAA,gBACxD7B,OAAA;YAAK+D,GAAG,EAAC,iCAAiC;YAACD,GAAG,EAAC,oBAAoB;YAAChC,KAAK,EAAE;cAAEsD,MAAM,EAAE,EAAE;cAAEpD,YAAY,EAAE;YAAE;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9GpC,OAAA,CAACE,SAAS;YAACmF,MAAM;YAAAxD,QAAA,EAAC;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNpC,OAAA,CAACd,GAAG;UAACwD,EAAE,EAAE,EAAG;UAACe,EAAE,EAAE,CAAE;UAACd,EAAE,EAAE,CAAE;UAACb,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAF,QAAA,gBACxD7B,OAAA;YAAK+D,GAAG,EAAC,gCAAgC;YAACD,GAAG,EAAC,mBAAmB;YAAChC,KAAK,EAAE;cAAEsD,MAAM,EAAE,EAAE;cAAEpD,YAAY,EAAE;YAAE;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5GpC,OAAA,CAACE,SAAS;YAACmF,MAAM;YAAAxD,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACNpC,OAAA,CAACd,GAAG;UAACwD,EAAE,EAAE,EAAG;UAACe,EAAE,EAAE,CAAE;UAACd,EAAE,EAAE,CAAE;UAACb,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAF,QAAA,gBACxD7B,OAAA;YAAK+D,GAAG,EAAC,gCAAgC;YAACD,GAAG,EAAC,mBAAmB;YAAChC,KAAK,EAAE;cAAEsD,MAAM,EAAE,EAAE;cAAEpD,YAAY,EAAE;YAAE;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5GpC,OAAA,CAACE,SAAS;YAACmF,MAAM;YAAAxD,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC7B,EAAA,CAhMID,YAAsB;AAAAgF,EAAA,GAAtBhF,YAAsB;AAkM5B,eAAeA,YAAY;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}