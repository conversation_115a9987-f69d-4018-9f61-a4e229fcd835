{"ast": null, "code": "var _jsxFileName = \"C:\\\\customized\\\\ShouChuang Sporting\\\\\\u5EFA\\u7AD9\\u8D44\\u6599\\u6536\\u96C6\\uFF08\\u6700\\u65B0\\u7248\\uFF09\\\\shouchuang-website\\\\src\\\\components\\\\WhatsAppContact.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Modal, Typography } from 'antd';\nimport { getImagePath } from '../utils/imageUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Text\n} = Typography;\nconst WhatsAppContact = ({\n  imageSrc,\n  altText = \"WhatsApp Contact\",\n  description = \"Contact us via WhatsApp:\",\n  className = \"\",\n  style = {}\n}) => {\n  _s();\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const showModal = () => {\n    setIsModalVisible(true);\n  };\n  const handleCancel = () => {\n    setIsModalVisible(false);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: style,\n      children: [description && /*#__PURE__*/_jsxDEV(Text, {\n        style: {\n          color: 'rgba(255,255,255,0.65)',\n          fontSize: '12px',\n          display: 'block',\n          marginBottom: 8\n        },\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: getImagePath(imageSrc),\n        alt: altText,\n        className: `whatsapp-image ${className}`,\n        onClick: showModal,\n        title: \"Click to view larger image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"WhatsApp Contact\",\n      open: isModalVisible,\n      onCancel: handleCancel,\n      footer: null,\n      width: 400,\n      centered: true,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: getImagePath(imageSrc),\n          alt: altText,\n          style: {\n            width: '100%',\n            height: 'auto',\n            borderRadius: 8,\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          style: {\n            fontSize: '14px',\n            color: '#666'\n          },\n          children: \"Scan the QR code above to contact us directly via WhatsApp\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(WhatsAppContact, \"ZFwHEtl1ZQoNaflbPbQvGOHlSaM=\");\n_c = WhatsAppContact;\nexport default WhatsAppContact;\nvar _c;\n$RefreshReg$(_c, \"WhatsAppContact\");", "map": {"version": 3, "names": ["React", "useState", "Modal", "Typography", "getImagePath", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Text", "WhatsAppContact", "imageSrc", "altText", "description", "className", "style", "_s", "isModalVisible", "setIsModalVisible", "showModal", "handleCancel", "children", "color", "fontSize", "display", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "onClick", "title", "open", "onCancel", "footer", "width", "centered", "textAlign", "height", "borderRadius", "_c", "$RefreshReg$"], "sources": ["C:/customized/ShouChuang Sporting/建站资料收集（最新版）/shouchuang-website/src/components/WhatsAppContact.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Modal, Typography } from 'antd';\nimport { getImagePath } from '../utils/imageUtils';\n\nconst { Text } = Typography;\n\ninterface WhatsAppContactProps {\n  imageSrc: string;\n  altText?: string;\n  description?: string;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst WhatsAppContact: React.FC<WhatsAppContactProps> = ({\n  imageSrc,\n  altText = \"WhatsApp Contact\",\n  description = \"Contact us via WhatsApp:\",\n  className = \"\",\n  style = {}\n}) => {\n  const [isModalVisible, setIsModalVisible] = useState(false);\n\n  const showModal = () => {\n    setIsModalVisible(true);\n  };\n\n  const handleCancel = () => {\n    setIsModalVisible(false);\n  };\n\n  return (\n    <>\n      <div style={style}>\n        {description && (\n          <Text style={{ \n            color: 'rgba(255,255,255,0.65)', \n            fontSize: '12px', \n            display: 'block', \n            marginBottom: 8 \n          }}>\n            {description}\n          </Text>\n        )}\n        <img\n          src={getImagePath(imageSrc)}\n          alt={altText}\n          className={`whatsapp-image ${className}`}\n          onClick={showModal}\n          title=\"Click to view larger image\"\n        />\n      </div>\n\n      <Modal\n        title=\"WhatsApp Contact\"\n        open={isModalVisible}\n        onCancel={handleCancel}\n        footer={null}\n        width={400}\n        centered\n      >\n        <div style={{ textAlign: 'center' }}>\n          <img\n            src={getImagePath(imageSrc)}\n            alt={altText}\n            style={{ \n              width: '100%', \n              height: 'auto', \n              borderRadius: 8,\n              marginBottom: 16\n            }}\n          />\n          <Text style={{ fontSize: '14px', color: '#666' }}>\n            Scan the QR code above to contact us directly via WhatsApp\n          </Text>\n        </div>\n      </Modal>\n    </>\n  );\n};\n\nexport default WhatsAppContact;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,KAAK,EAAEC,UAAU,QAAQ,MAAM;AACxC,SAASC,YAAY,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnD,MAAM;EAAEC;AAAK,CAAC,GAAGN,UAAU;AAU3B,MAAMO,eAA+C,GAAGA,CAAC;EACvDC,QAAQ;EACRC,OAAO,GAAG,kBAAkB;EAC5BC,WAAW,GAAG,0BAA0B;EACxCC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC;AACX,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMkB,SAAS,GAAGA,CAAA,KAAM;IACtBD,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBF,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,oBACEZ,OAAA,CAAAE,SAAA;IAAAa,QAAA,gBACEf,OAAA;MAAKS,KAAK,EAAEA,KAAM;MAAAM,QAAA,GACfR,WAAW,iBACVP,OAAA,CAACG,IAAI;QAACM,KAAK,EAAE;UACXO,KAAK,EAAE,wBAAwB;UAC/BC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,OAAO;UAChBC,YAAY,EAAE;QAChB,CAAE;QAAAJ,QAAA,EACCR;MAAW;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACP,eACDvB,OAAA;QACEwB,GAAG,EAAE1B,YAAY,CAACO,QAAQ,CAAE;QAC5BoB,GAAG,EAAEnB,OAAQ;QACbE,SAAS,EAAE,kBAAkBA,SAAS,EAAG;QACzCkB,OAAO,EAAEb,SAAU;QACnBc,KAAK,EAAC;MAA4B;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENvB,OAAA,CAACJ,KAAK;MACJ+B,KAAK,EAAC,kBAAkB;MACxBC,IAAI,EAAEjB,cAAe;MACrBkB,QAAQ,EAAEf,YAAa;MACvBgB,MAAM,EAAE,IAAK;MACbC,KAAK,EAAE,GAAI;MACXC,QAAQ;MAAAjB,QAAA,eAERf,OAAA;QAAKS,KAAK,EAAE;UAAEwB,SAAS,EAAE;QAAS,CAAE;QAAAlB,QAAA,gBAClCf,OAAA;UACEwB,GAAG,EAAE1B,YAAY,CAACO,QAAQ,CAAE;UAC5BoB,GAAG,EAAEnB,OAAQ;UACbG,KAAK,EAAE;YACLsB,KAAK,EAAE,MAAM;YACbG,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,CAAC;YACfhB,YAAY,EAAE;UAChB;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFvB,OAAA,CAACG,IAAI;UAACM,KAAK,EAAE;YAAEQ,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE;UAAO,CAAE;UAAAD,QAAA,EAAC;QAElD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAACb,EAAA,CAjEIN,eAA+C;AAAAgC,EAAA,GAA/ChC,eAA+C;AAmErD,eAAeA,eAAe;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}