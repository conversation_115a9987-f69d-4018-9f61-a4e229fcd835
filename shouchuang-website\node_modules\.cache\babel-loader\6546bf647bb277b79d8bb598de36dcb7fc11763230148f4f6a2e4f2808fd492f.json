{"ast": null, "code": "var _jsxFileName = \"C:\\\\customized\\\\ShouChuang Sporting\\\\\\u5EFA\\u7AD9\\u8D44\\u6599\\u6536\\u96C6\\uFF08\\u6700\\u65B0\\u7248\\uFF09\\\\shouchuang-website\\\\src\\\\layouts\\\\MainLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Outlet, Link, useLocation } from 'react-router-dom';\nimport { Layout, Menu, Button, Drawer, Row, Col, Typography, Space } from 'antd';\nimport { HomeOutlined, InfoCircleOutlined, AppstoreOutlined, BuildOutlined, ToolOutlined, ContactsOutlined, MenuOutlined, GlobalOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Content,\n  Footer\n} = Layout;\nconst {\n  Title,\n  Text\n} = Typography;\nconst MainLayout = () => {\n  _s();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const location = useLocation();\n  const currentPath = location.pathname;\n  const menuItems = [{\n    key: '/',\n    icon: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this),\n    label: /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/\",\n      children: \"Home\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 14\n    }, this)\n  }, {\n    key: '/about',\n    icon: /*#__PURE__*/_jsxDEV(InfoCircleOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this),\n    label: /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/about\",\n      children: \"About Us\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 14\n    }, this)\n  }, {\n    key: '/products',\n    icon: /*#__PURE__*/_jsxDEV(AppstoreOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this),\n    label: /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/products\",\n      children: \"Products\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 14\n    }, this)\n  }, {\n    key: '/factory',\n    icon: /*#__PURE__*/_jsxDEV(BuildOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this),\n    label: /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/factory\",\n      children: \"Factory\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 14\n    }, this)\n  }, {\n    key: '/services',\n    icon: /*#__PURE__*/_jsxDEV(ToolOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this),\n    label: /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/services\",\n      children: \"OEM/ODM\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 14\n    }, this)\n  }, {\n    key: '/contact',\n    icon: /*#__PURE__*/_jsxDEV(ContactsOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }, this),\n    label: /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/contact\",\n      children: \"Contact\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 14\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    className: \"layout\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      style: {\n        position: 'sticky',\n        top: 0,\n        zIndex: 1,\n        width: '100%',\n        padding: '0 24px',\n        background: '#fff',\n        boxShadow: '0 2px 8px rgba(0,0,0,0.06)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/logo/logo.jpg\",\n                alt: \"XLL Logo\",\n                style: {\n                  height: 40,\n                  marginRight: 10\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                style: {\n                  margin: 0,\n                  color: '#0056b3'\n                },\n                children: \"XLL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 0,\n          md: 16,\n          children: /*#__PURE__*/_jsxDEV(Menu, {\n            mode: \"horizontal\",\n            selectedKeys: [currentPath],\n            items: menuItems,\n            style: {\n              justifyContent: 'flex-end',\n              border: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 4,\n          md: 0,\n          style: {\n            textAlign: 'right'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: /*#__PURE__*/_jsxDEV(MenuOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 21\n            }, this),\n            onClick: () => setMobileMenuOpen(true),\n            style: {\n              fontSize: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n        title: \"Menu\",\n        placement: \"right\",\n        onClose: () => setMobileMenuOpen(false),\n        open: mobileMenuOpen,\n        width: 250,\n        children: /*#__PURE__*/_jsxDEV(Menu, {\n          mode: \"vertical\",\n          selectedKeys: [currentPath],\n          items: menuItems,\n          onClick: () => setMobileMenuOpen(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Content, {\n      children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {\n      style: {\n        background: '#001529',\n        color: '#fff',\n        padding: '40px 24px 24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [24, 24],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              style: {\n                color: '#fff',\n                marginBottom: 16\n              },\n              children: \"XLL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              style: {\n                color: 'rgba(255,255,255,0.65)'\n              },\n              children: \"Professional OEM/ODM Helmet Manufacturer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 5,\n            style: {\n              color: '#fff',\n              marginBottom: 16\n            },\n            children: \"Quick Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            mode: \"vertical\",\n            theme: \"dark\",\n            style: {\n              background: 'transparent',\n              border: 'none'\n            },\n            items: menuItems\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 5,\n            style: {\n              color: '#fff',\n              marginBottom: 16\n            },\n            children: \"Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              color: 'rgba(255,255,255,0.65)',\n              paddingLeft: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: 8\n              },\n              children: \"Road Cycling Helmet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: 8\n              },\n              children: \"Mountain Bike Helmet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: 8\n              },\n              children: \"Kids Helmet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: 8\n              },\n              children: \"Smart Helmet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: 8\n              },\n              children: \"Skateboard Helmet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: 8\n              },\n              children: \"Snow Helmet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: 8\n              },\n              children: \"Motorcycle Helmet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: 8\n              },\n              children: \"Horse Riding Helmet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        style: {\n          marginTop: 40,\n          paddingTop: 20,\n          borderTop: '1px solid rgba(255,255,255,0.1)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            style: {\n              color: 'rgba(255,255,255,0.45)'\n            },\n            children: [\"\\xA9 \", new Date().getFullYear(), \" XLL Sporting Goods Co., Ltd. All Rights Reserved.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"text\",\n              icon: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 41\n              }, this),\n              style: {\n                color: 'rgba(255,255,255,0.65)'\n              },\n              children: \"English\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(MainLayout, \"Bs2oUFLDOi+VE0tcASeCsSYgpZE=\", false, function () {\n  return [useLocation];\n});\n_c = MainLayout;\nexport default MainLayout;\nvar _c;\n$RefreshReg$(_c, \"MainLayout\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "Link", "useLocation", "Layout", "<PERSON><PERSON>", "<PERSON><PERSON>", "Drawer", "Row", "Col", "Typography", "Space", "HomeOutlined", "InfoCircleOutlined", "AppstoreOutlined", "BuildOutlined", "ToolOutlined", "ContactsOutlined", "MenuOutlined", "GlobalOutlined", "jsxDEV", "_jsxDEV", "Header", "Content", "Footer", "Title", "Text", "MainLayout", "_s", "mobileMenuOpen", "setMobileMenuOpen", "location", "currentPath", "pathname", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "to", "children", "className", "style", "position", "top", "zIndex", "width", "padding", "background", "boxShadow", "justify", "align", "display", "alignItems", "src", "alt", "height", "marginRight", "level", "margin", "color", "xs", "md", "mode", "<PERSON><PERSON><PERSON><PERSON>", "items", "justifyContent", "border", "textAlign", "type", "onClick", "fontSize", "title", "placement", "onClose", "open", "gutter", "sm", "direction", "size", "marginBottom", "theme", "paddingLeft", "marginTop", "paddingTop", "borderTop", "Date", "getFullYear", "_c", "$RefreshReg$"], "sources": ["C:/customized/ShouChuang Sporting/建站资料收集（最新版）/shouchuang-website/src/layouts/MainLayout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet, Link, useLocation } from 'react-router-dom';\nimport { Layout, Menu, But<PERSON>, Drawer, Row, Col, Typography, Space } from 'antd';\nimport {\n  HomeOutlined,\n  InfoCircleOutlined,\n  AppstoreOutlined,\n  BuildOutlined,\n  ToolOutlined,\n  ContactsOutlined,\n  MenuOutlined,\n  GlobalOutlined,\n} from '@ant-design/icons';\nimport { getImagePath } from '../utils/imageUtils';\n\nconst { Header, Content, Footer } = Layout;\nconst { Title, Text } = Typography;\n\nconst MainLayout: React.FC = () => {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const location = useLocation();\n  const currentPath = location.pathname;\n\n  const menuItems = [\n    {\n      key: '/',\n      icon: <HomeOutlined />,\n      label: <Link to=\"/\">Home</Link>,\n    },\n    {\n      key: '/about',\n      icon: <InfoCircleOutlined />,\n      label: <Link to=\"/about\">About Us</Link>,\n    },\n    {\n      key: '/products',\n      icon: <AppstoreOutlined />,\n      label: <Link to=\"/products\">Products</Link>,\n    },\n    {\n      key: '/factory',\n      icon: <BuildOutlined />,\n      label: <Link to=\"/factory\">Factory</Link>,\n    },\n    {\n      key: '/services',\n      icon: <ToolOutlined />,\n      label: <Link to=\"/services\">OEM/ODM</Link>,\n    },\n    {\n      key: '/contact',\n      icon: <ContactsOutlined />,\n      label: <Link to=\"/contact\">Contact</Link>,\n    },\n  ];\n\n  return (\n    <Layout className=\"layout\">\n      <Header style={{ position: 'sticky', top: 0, zIndex: 1, width: '100%', padding: '0 24px', background: '#fff', boxShadow: '0 2px 8px rgba(0,0,0,0.06)' }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Link to=\"/\">\n              <div style={{ display: 'flex', alignItems: 'center' }}>\n                <img src=\"/logo/logo.jpg\" alt=\"XLL Logo\" style={{ height: 40, marginRight: 10 }} />\n                <Title level={4} style={{ margin: 0, color: '#0056b3' }}>XLL</Title>\n              </div>\n            </Link>\n          </Col>\n          <Col xs={0} md={16}>\n            <Menu\n              mode=\"horizontal\"\n              selectedKeys={[currentPath]}\n              items={menuItems}\n              style={{ justifyContent: 'flex-end', border: 'none' }}\n            />\n          </Col>\n          <Col xs={4} md={0} style={{ textAlign: 'right' }}>\n            <Button\n              type=\"text\"\n              icon={<MenuOutlined />}\n              onClick={() => setMobileMenuOpen(true)}\n              style={{ fontSize: '16px' }}\n            />\n          </Col>\n        </Row>\n        <Drawer\n          title=\"Menu\"\n          placement=\"right\"\n          onClose={() => setMobileMenuOpen(false)}\n          open={mobileMenuOpen}\n          width={250}\n        >\n          <Menu\n            mode=\"vertical\"\n            selectedKeys={[currentPath]}\n            items={menuItems}\n            onClick={() => setMobileMenuOpen(false)}\n          />\n        </Drawer>\n      </Header>\n      <Content>\n        <Outlet />\n      </Content>\n      <Footer style={{ background: '#001529', color: '#fff', padding: '40px 24px 24px' }}>\n        <Row gutter={[24, 24]}>\n          <Col xs={24} sm={8}>\n            <Space direction=\"vertical\" size=\"small\">\n              <Title level={4} style={{ color: '#fff', marginBottom: 16 }}>XLL</Title>\n              <Text style={{ color: 'rgba(255,255,255,0.65)' }}>\n                Professional OEM/ODM Helmet Manufacturer\n              </Text>\n              {/* <Text style={{ color: 'rgba(255,255,255,0.65)' }}>\n                ShouChuang Sporting Goods Co., Ltd.\n              </Text> */}\n            </Space>\n          </Col>\n          <Col xs={24} sm={8}>\n            <Title level={5} style={{ color: '#fff', marginBottom: 16 }}>Quick Links</Title>\n            <Menu\n              mode=\"vertical\"\n              theme=\"dark\"\n              style={{ background: 'transparent', border: 'none' }}\n              items={menuItems}\n            />\n          </Col>\n          <Col xs={24} sm={8}>\n            <Title level={5} style={{ color: '#fff', marginBottom: 16 }}>Products</Title>\n            <ul style={{ color: 'rgba(255,255,255,0.65)', paddingLeft: 0 }}>\n              <li style={{ marginBottom: 8 }}>Road Cycling Helmet</li>\n              <li style={{ marginBottom: 8 }}>Mountain Bike Helmet</li>\n              <li style={{ marginBottom: 8 }}>Kids Helmet</li>\n              <li style={{ marginBottom: 8 }}>Smart Helmet</li>\n              <li style={{ marginBottom: 8 }}>Skateboard Helmet</li>\n              <li style={{ marginBottom: 8 }}>Snow Helmet</li>\n              <li style={{ marginBottom: 8 }}>Motorcycle Helmet</li>\n              <li style={{ marginBottom: 8 }}>Horse Riding Helmet</li>\n            </ul>\n          </Col>\n        </Row>\n        <Row justify=\"space-between\" align=\"middle\" style={{ marginTop: 40, paddingTop: 20, borderTop: '1px solid rgba(255,255,255,0.1)' }}>\n          <Col>\n            <Text style={{ color: 'rgba(255,255,255,0.45)' }}>\n              &copy; {new Date().getFullYear()} XLL Sporting Goods Co., Ltd. All Rights Reserved.\n            </Text>\n          </Col>\n          <Col>\n            <Space>\n              <Button type=\"text\" icon={<GlobalOutlined />} style={{ color: 'rgba(255,255,255,0.65)' }}>\n                English\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Footer>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAC5D,SAASC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,UAAU,EAAEC,KAAK,QAAQ,MAAM;AAChF,SACEC,YAAY,EACZC,kBAAkB,EAClBC,gBAAgB,EAChBC,aAAa,EACbC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,EACZC,cAAc,QACT,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3B,MAAM;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAO,CAAC,GAAGpB,MAAM;AAC1C,MAAM;EAAEqB,KAAK;EAAEC;AAAK,CAAC,GAAGhB,UAAU;AAElC,MAAMiB,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM+B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,WAAW,GAAGD,QAAQ,CAACE,QAAQ;EAErC,MAAMC,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,GAAG;IACRC,IAAI,eAAEf,OAAA,CAACT,YAAY;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,eAAEpB,OAAA,CAACnB,IAAI;MAACwC,EAAE,EAAC,GAAG;MAAAC,QAAA,EAAC;IAAI;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAChC,CAAC,EACD;IACEL,GAAG,EAAE,QAAQ;IACbC,IAAI,eAAEf,OAAA,CAACR,kBAAkB;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,KAAK,eAAEpB,OAAA,CAACnB,IAAI;MAACwC,EAAE,EAAC,QAAQ;MAAAC,QAAA,EAAC;IAAQ;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACzC,CAAC,EACD;IACEL,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEf,OAAA,CAACP,gBAAgB;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,eAAEpB,OAAA,CAACnB,IAAI;MAACwC,EAAE,EAAC,WAAW;MAAAC,QAAA,EAAC;IAAQ;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAC5C,CAAC,EACD;IACEL,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEf,OAAA,CAACN,aAAa;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,eAAEpB,OAAA,CAACnB,IAAI;MAACwC,EAAE,EAAC,UAAU;MAAAC,QAAA,EAAC;IAAO;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAC1C,CAAC,EACD;IACEL,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEf,OAAA,CAACL,YAAY;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,eAAEpB,OAAA,CAACnB,IAAI;MAACwC,EAAE,EAAC,WAAW;MAAAC,QAAA,EAAC;IAAO;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAC3C,CAAC,EACD;IACEL,GAAG,EAAE,UAAU;IACfC,IAAI,eAAEf,OAAA,CAACJ,gBAAgB;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,eAAEpB,OAAA,CAACnB,IAAI;MAACwC,EAAE,EAAC,UAAU;MAAAC,QAAA,EAAC;IAAO;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAC1C,CAAC,CACF;EAED,oBACEnB,OAAA,CAACjB,MAAM;IAACwC,SAAS,EAAC,QAAQ;IAAAD,QAAA,gBACxBtB,OAAA,CAACC,MAAM;MAACuB,KAAK,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,GAAG,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEC,OAAO,EAAE,QAAQ;QAAEC,UAAU,EAAE,MAAM;QAAEC,SAAS,EAAE;MAA6B,CAAE;MAAAT,QAAA,gBACtJtB,OAAA,CAACb,GAAG;QAAC6C,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAAX,QAAA,gBACzCtB,OAAA,CAACZ,GAAG;UAAAkC,QAAA,eACFtB,OAAA,CAACnB,IAAI;YAACwC,EAAE,EAAC,GAAG;YAAAC,QAAA,eACVtB,OAAA;cAAKwB,KAAK,EAAE;gBAAEU,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAb,QAAA,gBACpDtB,OAAA;gBAAKoC,GAAG,EAAC,gBAAgB;gBAACC,GAAG,EAAC,UAAU;gBAACb,KAAK,EAAE;kBAAEc,MAAM,EAAE,EAAE;kBAAEC,WAAW,EAAE;gBAAG;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnFnB,OAAA,CAACI,KAAK;gBAACoC,KAAK,EAAE,CAAE;gBAAChB,KAAK,EAAE;kBAAEiB,MAAM,EAAE,CAAC;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAApB,QAAA,EAAC;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNnB,OAAA,CAACZ,GAAG;UAACuD,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAtB,QAAA,eACjBtB,OAAA,CAAChB,IAAI;YACH6D,IAAI,EAAC,YAAY;YACjBC,YAAY,EAAE,CAACnC,WAAW,CAAE;YAC5BoC,KAAK,EAAElC,SAAU;YACjBW,KAAK,EAAE;cAAEwB,cAAc,EAAE,UAAU;cAAEC,MAAM,EAAE;YAAO;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNnB,OAAA,CAACZ,GAAG;UAACuD,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAACpB,KAAK,EAAE;YAAE0B,SAAS,EAAE;UAAQ,CAAE;UAAA5B,QAAA,eAC/CtB,OAAA,CAACf,MAAM;YACLkE,IAAI,EAAC,MAAM;YACXpC,IAAI,eAAEf,OAAA,CAACH,YAAY;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBiC,OAAO,EAAEA,CAAA,KAAM3C,iBAAiB,CAAC,IAAI,CAAE;YACvCe,KAAK,EAAE;cAAE6B,QAAQ,EAAE;YAAO;UAAE;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnB,OAAA,CAACd,MAAM;QACLoE,KAAK,EAAC,MAAM;QACZC,SAAS,EAAC,OAAO;QACjBC,OAAO,EAAEA,CAAA,KAAM/C,iBAAiB,CAAC,KAAK,CAAE;QACxCgD,IAAI,EAAEjD,cAAe;QACrBoB,KAAK,EAAE,GAAI;QAAAN,QAAA,eAEXtB,OAAA,CAAChB,IAAI;UACH6D,IAAI,EAAC,UAAU;UACfC,YAAY,EAAE,CAACnC,WAAW,CAAE;UAC5BoC,KAAK,EAAElC,SAAU;UACjBuC,OAAO,EAAEA,CAAA,KAAM3C,iBAAiB,CAAC,KAAK;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACTnB,OAAA,CAACE,OAAO;MAAAoB,QAAA,eACNtB,OAAA,CAACpB,MAAM;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACVnB,OAAA,CAACG,MAAM;MAACqB,KAAK,EAAE;QAAEM,UAAU,EAAE,SAAS;QAAEY,KAAK,EAAE,MAAM;QAAEb,OAAO,EAAE;MAAiB,CAAE;MAAAP,QAAA,gBACjFtB,OAAA,CAACb,GAAG;QAACuE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAApC,QAAA,gBACpBtB,OAAA,CAACZ,GAAG;UAACuD,EAAE,EAAE,EAAG;UAACgB,EAAE,EAAE,CAAE;UAAArC,QAAA,eACjBtB,OAAA,CAACV,KAAK;YAACsE,SAAS,EAAC,UAAU;YAACC,IAAI,EAAC,OAAO;YAAAvC,QAAA,gBACtCtB,OAAA,CAACI,KAAK;cAACoC,KAAK,EAAE,CAAE;cAAChB,KAAK,EAAE;gBAAEkB,KAAK,EAAE,MAAM;gBAAEoB,YAAY,EAAE;cAAG,CAAE;cAAAxC,QAAA,EAAC;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxEnB,OAAA,CAACK,IAAI;cAACmB,KAAK,EAAE;gBAAEkB,KAAK,EAAE;cAAyB,CAAE;cAAApB,QAAA,EAAC;YAElD;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNnB,OAAA,CAACZ,GAAG;UAACuD,EAAE,EAAE,EAAG;UAACgB,EAAE,EAAE,CAAE;UAAArC,QAAA,gBACjBtB,OAAA,CAACI,KAAK;YAACoC,KAAK,EAAE,CAAE;YAAChB,KAAK,EAAE;cAAEkB,KAAK,EAAE,MAAM;cAAEoB,YAAY,EAAE;YAAG,CAAE;YAAAxC,QAAA,EAAC;UAAW;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChFnB,OAAA,CAAChB,IAAI;YACH6D,IAAI,EAAC,UAAU;YACfkB,KAAK,EAAC,MAAM;YACZvC,KAAK,EAAE;cAAEM,UAAU,EAAE,aAAa;cAAEmB,MAAM,EAAE;YAAO,CAAE;YACrDF,KAAK,EAAElC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNnB,OAAA,CAACZ,GAAG;UAACuD,EAAE,EAAE,EAAG;UAACgB,EAAE,EAAE,CAAE;UAAArC,QAAA,gBACjBtB,OAAA,CAACI,KAAK;YAACoC,KAAK,EAAE,CAAE;YAAChB,KAAK,EAAE;cAAEkB,KAAK,EAAE,MAAM;cAAEoB,YAAY,EAAE;YAAG,CAAE;YAAAxC,QAAA,EAAC;UAAQ;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7EnB,OAAA;YAAIwB,KAAK,EAAE;cAAEkB,KAAK,EAAE,wBAAwB;cAAEsB,WAAW,EAAE;YAAE,CAAE;YAAA1C,QAAA,gBAC7DtB,OAAA;cAAIwB,KAAK,EAAE;gBAAEsC,YAAY,EAAE;cAAE,CAAE;cAAAxC,QAAA,EAAC;YAAmB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDnB,OAAA;cAAIwB,KAAK,EAAE;gBAAEsC,YAAY,EAAE;cAAE,CAAE;cAAAxC,QAAA,EAAC;YAAoB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzDnB,OAAA;cAAIwB,KAAK,EAAE;gBAAEsC,YAAY,EAAE;cAAE,CAAE;cAAAxC,QAAA,EAAC;YAAW;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDnB,OAAA;cAAIwB,KAAK,EAAE;gBAAEsC,YAAY,EAAE;cAAE,CAAE;cAAAxC,QAAA,EAAC;YAAY;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDnB,OAAA;cAAIwB,KAAK,EAAE;gBAAEsC,YAAY,EAAE;cAAE,CAAE;cAAAxC,QAAA,EAAC;YAAiB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtDnB,OAAA;cAAIwB,KAAK,EAAE;gBAAEsC,YAAY,EAAE;cAAE,CAAE;cAAAxC,QAAA,EAAC;YAAW;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDnB,OAAA;cAAIwB,KAAK,EAAE;gBAAEsC,YAAY,EAAE;cAAE,CAAE;cAAAxC,QAAA,EAAC;YAAiB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtDnB,OAAA;cAAIwB,KAAK,EAAE;gBAAEsC,YAAY,EAAE;cAAE,CAAE;cAAAxC,QAAA,EAAC;YAAmB;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnB,OAAA,CAACb,GAAG;QAAC6C,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAACT,KAAK,EAAE;UAAEyC,SAAS,EAAE,EAAE;UAAEC,UAAU,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAkC,CAAE;QAAA7C,QAAA,gBACjItB,OAAA,CAACZ,GAAG;UAAAkC,QAAA,eACFtB,OAAA,CAACK,IAAI;YAACmB,KAAK,EAAE;cAAEkB,KAAK,EAAE;YAAyB,CAAE;YAAApB,QAAA,GAAC,OACzC,EAAC,IAAI8C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAC,oDACnC;UAAA;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNnB,OAAA,CAACZ,GAAG;UAAAkC,QAAA,eACFtB,OAAA,CAACV,KAAK;YAAAgC,QAAA,eACJtB,OAAA,CAACf,MAAM;cAACkE,IAAI,EAAC,MAAM;cAACpC,IAAI,eAAEf,OAAA,CAACF,cAAc;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACK,KAAK,EAAE;gBAAEkB,KAAK,EAAE;cAAyB,CAAE;cAAApB,QAAA,EAAC;YAE1F;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACZ,EAAA,CA1IID,UAAoB;EAAA,QAEPxB,WAAW;AAAA;AAAAwF,EAAA,GAFxBhE,UAAoB;AA4I1B,eAAeA,UAAU;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}