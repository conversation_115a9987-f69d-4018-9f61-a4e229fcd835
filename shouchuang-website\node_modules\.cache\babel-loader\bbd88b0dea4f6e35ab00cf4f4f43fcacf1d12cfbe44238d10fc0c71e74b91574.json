{"ast": null, "code": "var _jsxFileName = \"C:\\\\customized\\\\ShouChuang Sporting\\\\\\u5EFA\\u7AD9\\u8D44\\u6599\\u6536\\u96C6\\uFF08\\u6700\\u65B0\\u7248\\uFF09\\\\shouchuang-website\\\\src\\\\pages\\\\Home.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Typography, Button, Row, Col, Card, Space } from 'antd';\nimport { RightOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport { products, services } from '../models/mockData';\nimport { getImagePath } from '../utils/imageUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph\n} = Typography;\nconst {\n  Meta\n} = Card;\nconst HomePage = () => {\n  // Select featured products (first 4)\n  const featuredProducts = products.slice(0, 4);\n\n  // Select featured services (first 3)\n  const featuredServices = services.slice(0, 3);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-section\",\n      style: {\n        backgroundImage: `url(${getImagePath('/images/hero-bg.jpg')})`\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          className: \"hero-title\",\n          style: {\n            color: '#fff'\n          },\n          children: \"XLL Sporting Goods Co., Ltd.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          className: \"hero-description\",\n          style: {\n            color: '#fff'\n          },\n          children: \"Professional OEM/ODM Helmet Manufacturer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/products\",\n              children: \"Explore Our Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            size: \"large\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [48, 24],\n          align: \"middle\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative',\n                borderRadius: 8,\n                overflow: 'hidden'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"video\", {\n                controls: true,\n                style: {\n                  width: '100%',\n                  height: 'auto',\n                  borderRadius: 8\n                },\n                poster: getImagePath('/images/about-img.jpg'),\n                children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                  src: getImagePath('/video/20250714092754.mp4'),\n                  type: \"video/mp4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 19\n                }, this), \"Your browser does not support the video tag.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 2,\n              children: \"About XLL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                fontSize: 16,\n                marginBottom: 16\n              },\n              children: \"XLL Sporting Goods Co., Ltd. is a professional helmet manufacturer specializing in the design, development, and production of high-quality helmets for various sports and activities.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                fontSize: 16,\n                marginBottom: 16\n              },\n              children: \"With our state-of-the-art manufacturing facility and experienced R&D team, we provide exceptional OEM and ODM services to clients worldwide.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                fontSize: 16,\n                marginBottom: 24\n              },\n              children: \"Our commitment to quality, innovation, and customer satisfaction has made us a trusted partner for sports equipment brands globally.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              size: \"large\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/about\",\n                children: [\"Learn More About Us \", /*#__PURE__*/_jsxDEV(RightOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 55\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section section-dark\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            textAlign: 'center',\n            marginBottom: 48\n          },\n          children: \"Our Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [24, 24],\n          children: featuredProducts.map(product => /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            lg: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              hoverable: true,\n              className: \"product-card\",\n              cover: /*#__PURE__*/_jsxDEV(\"img\", {\n                alt: product.name,\n                src: getImagePath(product.image),\n                style: {\n                  height: 200,\n                  objectFit: 'cover'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 26\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(Meta, {\n                title: product.name,\n                description: product.description.length > 100 ? `${product.description.substring(0, 100)}...` : product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 16\n                },\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/products#${product.id}`,\n                    children: \"View Details\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 101,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this)\n          }, product.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            marginTop: 40\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/products\",\n              children: [\"View All Products \", /*#__PURE__*/_jsxDEV(RightOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 54\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            textAlign: 'center',\n            marginBottom: 16\n          },\n          children: \"OEM & ODM Services\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          style: {\n            textAlign: 'center',\n            fontSize: 16,\n            maxWidth: 800,\n            margin: '0 auto 48px'\n          },\n          children: \"We provide comprehensive OEM and ODM services for helmet manufacturing, offering customized solutions to meet your specific requirements.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [24, 24],\n          children: featuredServices.map(service => /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"service-card\",\n              style: {\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                children: service.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                children: service.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this)\n          }, service.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            marginTop: 40\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/services\",\n              children: [\"Learn More About Our Services \", /*#__PURE__*/_jsxDEV(RightOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 66\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section section-dark\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            textAlign: 'center',\n            marginBottom: 48\n          },\n          children: \"Why Choose Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [24, 24],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              style: {\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                children: \"Quality Assurance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                children: \"All our helmets undergo rigorous testing to meet or exceed international safety standards, ensuring the highest quality products for your brand.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 30\n                  }, this), \" CE Certified\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 30\n                  }, this), \" ASTM/SEI Certified\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 30\n                  }, this), \" DOT/ECE Certified\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              style: {\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                children: \"Customization Options\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                children: \"We offer extensive customization options for all our helmet models, allowing you to create unique products that align with your brand identity.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 30\n                  }, this), \" Custom Colors & Graphics\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 30\n                  }, this), \" Custom Packaging\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 30\n                  }, this), \" Custom Features & Accessories\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              style: {\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 4,\n                children: \"Competitive Advantage\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                children: \"Partner with us to gain a competitive edge in the market with innovative helmet designs, cost-effective manufacturing, and reliable delivery.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 30\n                  }, this), \" Innovative Designs\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 30\n                  }, this), \" Competitive Pricing\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                    style: {\n                      color: '#52c41a',\n                      marginRight: 8\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 30\n                  }, this), \" On-time Delivery\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"section\",\n      style: {\n        background: '#0056b3',\n        color: '#fff'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            color: '#fff',\n            marginBottom: 24\n          },\n          children: \"Ready to Start Your Project?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          style: {\n            fontSize: 16,\n            marginBottom: 32,\n            color: '#fff'\n          },\n          children: \"Contact us today to discuss your helmet manufacturing needs and discover how we can help bring your ideas to life.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"large\",\n          style: {\n            background: '#fff',\n            color: '#0056b3'\n          },\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/contact\",\n            children: \"Contact Us Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "Link", "Typography", "<PERSON><PERSON>", "Row", "Col", "Card", "Space", "RightOutlined", "CheckCircleOutlined", "products", "services", "getImagePath", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "Meta", "HomePage", "featuredProducts", "slice", "featuredServices", "children", "className", "style", "backgroundImage", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "size", "to", "gutter", "align", "xs", "md", "position", "borderRadius", "overflow", "controls", "width", "height", "poster", "src", "level", "fontSize", "marginBottom", "textAlign", "map", "product", "sm", "lg", "hoverable", "cover", "alt", "name", "image", "objectFit", "title", "description", "length", "substring", "marginTop", "id", "max<PERSON><PERSON><PERSON>", "margin", "service", "direction", "marginRight", "background", "_c", "$RefreshReg$"], "sources": ["C:/customized/ShouChuang Sporting/建站资料收集（最新版）/shouchuang-website/src/pages/Home.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Typography, Button, Row, Col, Card, Carousel, Space, Divider } from 'antd';\nimport { RightOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport { products, services } from '../models/mockData';\nimport { getImagePath } from '../utils/imageUtils';\n\nconst { Title, Paragraph } = Typography;\nconst { Meta } = Card;\n\nconst HomePage: React.FC = () => {\n  // Select featured products (first 4)\n  const featuredProducts = products.slice(0, 4);\n\n  // Select featured services (first 3)\n  const featuredServices = services.slice(0, 3);\n\n  return (\n    <div>\n      {/* Hero Section */}\n      <div\n        className=\"hero-section\"\n        style={{\n          backgroundImage: `url(${getImagePath('/images/hero-bg.jpg')})`,\n        }}\n      >\n        <div className=\"hero-content\">\n          <Title className=\"hero-title\" style={{ color: '#fff' }}>\n            XLL Sporting Goods Co., Ltd.\n          </Title>\n          {/* <Title level={2} className=\"hero-subtitle\" style={{ color: '#fff' }}>\n            ShouChuang Sporting Goods Co., Ltd.\n          </Title> */}\n          <Paragraph className=\"hero-description\" style={{ color: '#fff' }}>\n            Professional OEM/ODM Helmet Manufacturer\n          </Paragraph>\n          <Space>\n            <Button type=\"primary\" size=\"large\">\n              <Link to=\"/products\">Explore Our Products</Link>\n            </Button>\n            <Button size=\"large\">\n              <Link to=\"/contact\">Contact Us</Link>\n            </Button>\n          </Space>\n        </div>\n      </div>\n\n      {/* About Section */}\n      <section className=\"section\">\n        <div className=\"container\">\n          <Row gutter={[48, 24]} align=\"middle\">\n            <Col xs={24} md={12}>\n              <div style={{ position: 'relative', borderRadius: 8, overflow: 'hidden' }}>\n                <video\n                  controls\n                  style={{ width: '100%', height: 'auto', borderRadius: 8 }}\n                  poster={getImagePath('/images/about-img.jpg')}\n                >\n                  <source src={getImagePath('/video/20250714092754.mp4')} type=\"video/mp4\" />\n                  Your browser does not support the video tag.\n                </video>\n              </div>\n            </Col>\n            <Col xs={24} md={12}>\n              <Title level={2}>About XLL</Title>\n              <Paragraph style={{ fontSize: 16, marginBottom: 16 }}>\n                XLL Sporting Goods Co., Ltd. is a professional helmet manufacturer specializing in the design, development, and production of high-quality helmets for various sports and activities.\n              </Paragraph>\n              <Paragraph style={{ fontSize: 16, marginBottom: 16 }}>\n                With our state-of-the-art manufacturing facility and experienced R&D team, we provide exceptional OEM and ODM services to clients worldwide.\n              </Paragraph>\n              <Paragraph style={{ fontSize: 16, marginBottom: 24 }}>\n                Our commitment to quality, innovation, and customer satisfaction has made us a trusted partner for sports equipment brands globally.\n              </Paragraph>\n              <Button type=\"primary\" size=\"large\">\n                <Link to=\"/about\">Learn More About Us <RightOutlined /></Link>\n              </Button>\n            </Col>\n          </Row>\n        </div>\n      </section>\n\n      {/* Products Section */}\n      <section className=\"section section-dark\">\n        <div className=\"container\">\n          <Title level={2} style={{ textAlign: 'center', marginBottom: 48 }}>Our Products</Title>\n          <Row gutter={[24, 24]}>\n            {featuredProducts.map(product => (\n              <Col xs={24} sm={12} lg={6} key={product.id}>\n                <Card\n                  hoverable\n                  className=\"product-card\"\n                  cover={<img alt={product.name} src={getImagePath(product.image)} style={{ height: 200, objectFit: 'cover' }} />}\n                >\n                  <Meta\n                    title={product.name}\n                    description={product.description.length > 100 ? `${product.description.substring(0, 100)}...` : product.description}\n                  />\n                  <div style={{ marginTop: 16 }}>\n                    <Button type=\"primary\">\n                      <Link to={`/products#${product.id}`}>View Details</Link>\n                    </Button>\n                  </div>\n                </Card>\n              </Col>\n            ))}\n          </Row>\n          <div style={{ textAlign: 'center', marginTop: 40 }}>\n            <Button type=\"primary\" size=\"large\">\n              <Link to=\"/products\">View All Products <RightOutlined /></Link>\n            </Button>\n          </div>\n        </div>\n      </section>\n\n      {/* Services Section */}\n      <section className=\"section\">\n        <div className=\"container\">\n          <Title level={2} style={{ textAlign: 'center', marginBottom: 16 }}>OEM & ODM Services</Title>\n          <Paragraph style={{ textAlign: 'center', fontSize: 16, maxWidth: 800, margin: '0 auto 48px' }}>\n            We provide comprehensive OEM and ODM services for helmet manufacturing, offering customized solutions to meet your specific requirements.\n          </Paragraph>\n          <Row gutter={[24, 24]}>\n            {featuredServices.map(service => (\n              <Col xs={24} md={8} key={service.id}>\n                <Card className=\"service-card\" style={{ height: '100%' }}>\n                  <Title level={4}>{service.title}</Title>\n                  <Paragraph>{service.description}</Paragraph>\n                </Card>\n              </Col>\n            ))}\n          </Row>\n          <div style={{ textAlign: 'center', marginTop: 40 }}>\n            <Button type=\"primary\" size=\"large\">\n              <Link to=\"/services\">Learn More About Our Services <RightOutlined /></Link>\n            </Button>\n          </div>\n        </div>\n      </section>\n\n      {/* Why Choose Us Section */}\n      <section className=\"section section-dark\">\n        <div className=\"container\">\n          <Title level={2} style={{ textAlign: 'center', marginBottom: 48 }}>Why Choose Us</Title>\n          <Row gutter={[24, 24]}>\n            <Col xs={24} md={8}>\n              <Card style={{ height: '100%' }}>\n                <Title level={4}>Quality Assurance</Title>\n                <Paragraph>\n                  All our helmets undergo rigorous testing to meet or exceed international safety standards, ensuring the highest quality products for your brand.\n                </Paragraph>\n                <Space direction=\"vertical\">\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> CE Certified</Paragraph>\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> ASTM/SEI Certified</Paragraph>\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> DOT/ECE Certified</Paragraph>\n                </Space>\n              </Card>\n            </Col>\n            <Col xs={24} md={8}>\n              <Card style={{ height: '100%' }}>\n                <Title level={4}>Customization Options</Title>\n                <Paragraph>\n                  We offer extensive customization options for all our helmet models, allowing you to create unique products that align with your brand identity.\n                </Paragraph>\n                <Space direction=\"vertical\">\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Custom Colors & Graphics</Paragraph>\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Custom Packaging</Paragraph>\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Custom Features & Accessories</Paragraph>\n                </Space>\n              </Card>\n            </Col>\n            <Col xs={24} md={8}>\n              <Card style={{ height: '100%' }}>\n                <Title level={4}>Competitive Advantage</Title>\n                <Paragraph>\n                  Partner with us to gain a competitive edge in the market with innovative helmet designs, cost-effective manufacturing, and reliable delivery.\n                </Paragraph>\n                <Space direction=\"vertical\">\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Innovative Designs</Paragraph>\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> Competitive Pricing</Paragraph>\n                  <Paragraph><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} /> On-time Delivery</Paragraph>\n                </Space>\n              </Card>\n            </Col>\n          </Row>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"section\" style={{ background: '#0056b3', color: '#fff' }}>\n        <div className=\"container\" style={{ textAlign: 'center' }}>\n          <Title level={2} style={{ color: '#fff', marginBottom: 24 }}>Ready to Start Your Project?</Title>\n          <Paragraph style={{ fontSize: 16, marginBottom: 32, color: '#fff' }}>\n            Contact us today to discuss your helmet manufacturing needs and discover how we can help bring your ideas to life.\n          </Paragraph>\n          <Button size=\"large\" style={{ background: '#fff', color: '#0056b3' }}>\n            <Link to=\"/contact\">Contact Us Now</Link>\n          </Button>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAYC,KAAK,QAAiB,MAAM;AACnF,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,mBAAmB;AACtE,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,YAAY,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAM;EAAEC,KAAK;EAAEC;AAAU,CAAC,GAAGd,UAAU;AACvC,MAAM;EAAEe;AAAK,CAAC,GAAGX,IAAI;AAErB,MAAMY,QAAkB,GAAGA,CAAA,KAAM;EAC/B;EACA,MAAMC,gBAAgB,GAAGT,QAAQ,CAACU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;EAE7C;EACA,MAAMC,gBAAgB,GAAGV,QAAQ,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAE7C,oBACEN,OAAA;IAAAQ,QAAA,gBAEER,OAAA;MACES,SAAS,EAAC,cAAc;MACxBC,KAAK,EAAE;QACLC,eAAe,EAAE,OAAOb,YAAY,CAAC,qBAAqB,CAAC;MAC7D,CAAE;MAAAU,QAAA,eAEFR,OAAA;QAAKS,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3BR,OAAA,CAACC,KAAK;UAACQ,SAAS,EAAC,YAAY;UAACC,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAExD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAIRhB,OAAA,CAACE,SAAS;UAACO,SAAS,EAAC,kBAAkB;UAACC,KAAK,EAAE;YAAEE,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAElE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZhB,OAAA,CAACP,KAAK;UAAAe,QAAA,gBACJR,OAAA,CAACX,MAAM;YAAC4B,IAAI,EAAC,SAAS;YAACC,IAAI,EAAC,OAAO;YAAAV,QAAA,eACjCR,OAAA,CAACb,IAAI;cAACgC,EAAE,EAAC,WAAW;cAAAX,QAAA,EAAC;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACThB,OAAA,CAACX,MAAM;YAAC6B,IAAI,EAAC,OAAO;YAAAV,QAAA,eAClBR,OAAA,CAACb,IAAI;cAACgC,EAAE,EAAC,UAAU;cAAAX,QAAA,EAAC;YAAU;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhB,OAAA;MAASS,SAAS,EAAC,SAAS;MAAAD,QAAA,eAC1BR,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBR,OAAA,CAACV,GAAG;UAAC8B,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACC,KAAK,EAAC,QAAQ;UAAAb,QAAA,gBACnCR,OAAA,CAACT,GAAG;YAAC+B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAf,QAAA,eAClBR,OAAA;cAAKU,KAAK,EAAE;gBAAEc,QAAQ,EAAE,UAAU;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAE;cAAAlB,QAAA,eACxER,OAAA;gBACE2B,QAAQ;gBACRjB,KAAK,EAAE;kBAAEkB,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE,MAAM;kBAAEJ,YAAY,EAAE;gBAAE,CAAE;gBAC1DK,MAAM,EAAEhC,YAAY,CAAC,uBAAuB,CAAE;gBAAAU,QAAA,gBAE9CR,OAAA;kBAAQ+B,GAAG,EAAEjC,YAAY,CAAC,2BAA2B,CAAE;kBAACmB,IAAI,EAAC;gBAAW;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gDAE7E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhB,OAAA,CAACT,GAAG;YAAC+B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAf,QAAA,gBAClBR,OAAA,CAACC,KAAK;cAAC+B,KAAK,EAAE,CAAE;cAAAxB,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClChB,OAAA,CAACE,SAAS;cAACQ,KAAK,EAAE;gBAAEuB,QAAQ,EAAE,EAAE;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAA1B,QAAA,EAAC;YAEtD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACZhB,OAAA,CAACE,SAAS;cAACQ,KAAK,EAAE;gBAAEuB,QAAQ,EAAE,EAAE;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAA1B,QAAA,EAAC;YAEtD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACZhB,OAAA,CAACE,SAAS;cAACQ,KAAK,EAAE;gBAAEuB,QAAQ,EAAE,EAAE;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAA1B,QAAA,EAAC;YAEtD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACZhB,OAAA,CAACX,MAAM;cAAC4B,IAAI,EAAC,SAAS;cAACC,IAAI,EAAC,OAAO;cAAAV,QAAA,eACjCR,OAAA,CAACb,IAAI;gBAACgC,EAAE,EAAC,QAAQ;gBAAAX,QAAA,GAAC,sBAAoB,eAAAR,OAAA,CAACN,aAAa;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhB,OAAA;MAASS,SAAS,EAAC,sBAAsB;MAAAD,QAAA,eACvCR,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxBR,OAAA,CAACC,KAAK;UAAC+B,KAAK,EAAE,CAAE;UAACtB,KAAK,EAAE;YAAEyB,SAAS,EAAE,QAAQ;YAAED,YAAY,EAAE;UAAG,CAAE;UAAA1B,QAAA,EAAC;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvFhB,OAAA,CAACV,GAAG;UAAC8B,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAZ,QAAA,EACnBH,gBAAgB,CAAC+B,GAAG,CAACC,OAAO,iBAC3BrC,OAAA,CAACT,GAAG;YAAC+B,EAAE,EAAE,EAAG;YAACgB,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzBR,OAAA,CAACR,IAAI;cACHgD,SAAS;cACT/B,SAAS,EAAC,cAAc;cACxBgC,KAAK,eAAEzC,OAAA;gBAAK0C,GAAG,EAAEL,OAAO,CAACM,IAAK;gBAACZ,GAAG,EAAEjC,YAAY,CAACuC,OAAO,CAACO,KAAK,CAAE;gBAAClC,KAAK,EAAE;kBAAEmB,MAAM,EAAE,GAAG;kBAAEgB,SAAS,EAAE;gBAAQ;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAR,QAAA,gBAEhHR,OAAA,CAACG,IAAI;gBACH2C,KAAK,EAAET,OAAO,CAACM,IAAK;gBACpBI,WAAW,EAAEV,OAAO,CAACU,WAAW,CAACC,MAAM,GAAG,GAAG,GAAG,GAAGX,OAAO,CAACU,WAAW,CAACE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GAAGZ,OAAO,CAACU;cAAY;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrH,CAAC,eACFhB,OAAA;gBAAKU,KAAK,EAAE;kBAAEwC,SAAS,EAAE;gBAAG,CAAE;gBAAA1C,QAAA,eAC5BR,OAAA,CAACX,MAAM;kBAAC4B,IAAI,EAAC,SAAS;kBAAAT,QAAA,eACpBR,OAAA,CAACb,IAAI;oBAACgC,EAAE,EAAE,aAAakB,OAAO,CAACc,EAAE,EAAG;oBAAA3C,QAAA,EAAC;kBAAY;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC,GAfwBqB,OAAO,CAACc,EAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBtC,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhB,OAAA;UAAKU,KAAK,EAAE;YAAEyB,SAAS,EAAE,QAAQ;YAAEe,SAAS,EAAE;UAAG,CAAE;UAAA1C,QAAA,eACjDR,OAAA,CAACX,MAAM;YAAC4B,IAAI,EAAC,SAAS;YAACC,IAAI,EAAC,OAAO;YAAAV,QAAA,eACjCR,OAAA,CAACb,IAAI;cAACgC,EAAE,EAAC,WAAW;cAAAX,QAAA,GAAC,oBAAkB,eAAAR,OAAA,CAACN,aAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhB,OAAA;MAASS,SAAS,EAAC,SAAS;MAAAD,QAAA,eAC1BR,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxBR,OAAA,CAACC,KAAK;UAAC+B,KAAK,EAAE,CAAE;UAACtB,KAAK,EAAE;YAAEyB,SAAS,EAAE,QAAQ;YAAED,YAAY,EAAE;UAAG,CAAE;UAAA1B,QAAA,EAAC;QAAkB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7FhB,OAAA,CAACE,SAAS;UAACQ,KAAK,EAAE;YAAEyB,SAAS,EAAE,QAAQ;YAAEF,QAAQ,EAAE,EAAE;YAAEmB,QAAQ,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAc,CAAE;UAAA7C,QAAA,EAAC;QAE/F;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZhB,OAAA,CAACV,GAAG;UAAC8B,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAZ,QAAA,EACnBD,gBAAgB,CAAC6B,GAAG,CAACkB,OAAO,iBAC3BtD,OAAA,CAACT,GAAG;YAAC+B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAf,QAAA,eACjBR,OAAA,CAACR,IAAI;cAACiB,SAAS,EAAC,cAAc;cAACC,KAAK,EAAE;gBAAEmB,MAAM,EAAE;cAAO,CAAE;cAAArB,QAAA,gBACvDR,OAAA,CAACC,KAAK;gBAAC+B,KAAK,EAAE,CAAE;gBAAAxB,QAAA,EAAE8C,OAAO,CAACR;cAAK;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxChB,OAAA,CAACE,SAAS;gBAAAM,QAAA,EAAE8C,OAAO,CAACP;cAAW;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC,GAJgBsC,OAAO,CAACH,EAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAK9B,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhB,OAAA;UAAKU,KAAK,EAAE;YAAEyB,SAAS,EAAE,QAAQ;YAAEe,SAAS,EAAE;UAAG,CAAE;UAAA1C,QAAA,eACjDR,OAAA,CAACX,MAAM;YAAC4B,IAAI,EAAC,SAAS;YAACC,IAAI,EAAC,OAAO;YAAAV,QAAA,eACjCR,OAAA,CAACb,IAAI;cAACgC,EAAE,EAAC,WAAW;cAAAX,QAAA,GAAC,gCAA8B,eAAAR,OAAA,CAACN,aAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhB,OAAA;MAASS,SAAS,EAAC,sBAAsB;MAAAD,QAAA,eACvCR,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAD,QAAA,gBACxBR,OAAA,CAACC,KAAK;UAAC+B,KAAK,EAAE,CAAE;UAACtB,KAAK,EAAE;YAAEyB,SAAS,EAAE,QAAQ;YAAED,YAAY,EAAE;UAAG,CAAE;UAAA1B,QAAA,EAAC;QAAa;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxFhB,OAAA,CAACV,GAAG;UAAC8B,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAZ,QAAA,gBACpBR,OAAA,CAACT,GAAG;YAAC+B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAf,QAAA,eACjBR,OAAA,CAACR,IAAI;cAACkB,KAAK,EAAE;gBAAEmB,MAAM,EAAE;cAAO,CAAE;cAAArB,QAAA,gBAC9BR,OAAA,CAACC,KAAK;gBAAC+B,KAAK,EAAE,CAAE;gBAAAxB,QAAA,EAAC;cAAiB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1ChB,OAAA,CAACE,SAAS;gBAAAM,QAAA,EAAC;cAEX;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACZhB,OAAA,CAACP,KAAK;gBAAC8D,SAAS,EAAC,UAAU;gBAAA/C,QAAA,gBACzBR,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACL,mBAAmB;oBAACe,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAE4C,WAAW,EAAE;oBAAE;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAAa;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACxGhB,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACL,mBAAmB;oBAACe,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAE4C,WAAW,EAAE;oBAAE;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uBAAmB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9GhB,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACL,mBAAmB;oBAACe,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAE4C,WAAW,EAAE;oBAAE;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,sBAAkB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNhB,OAAA,CAACT,GAAG;YAAC+B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAf,QAAA,eACjBR,OAAA,CAACR,IAAI;cAACkB,KAAK,EAAE;gBAAEmB,MAAM,EAAE;cAAO,CAAE;cAAArB,QAAA,gBAC9BR,OAAA,CAACC,KAAK;gBAAC+B,KAAK,EAAE,CAAE;gBAAAxB,QAAA,EAAC;cAAqB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9ChB,OAAA,CAACE,SAAS;gBAAAM,QAAA,EAAC;cAEX;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACZhB,OAAA,CAACP,KAAK;gBAAC8D,SAAS,EAAC,UAAU;gBAAA/C,QAAA,gBACzBR,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACL,mBAAmB;oBAACe,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAE4C,WAAW,EAAE;oBAAE;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,6BAAyB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpHhB,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACL,mBAAmB;oBAACe,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAE4C,WAAW,EAAE;oBAAE;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qBAAiB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5GhB,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACL,mBAAmB;oBAACe,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAE4C,WAAW,EAAE;oBAAE;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kCAA8B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNhB,OAAA,CAACT,GAAG;YAAC+B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAf,QAAA,eACjBR,OAAA,CAACR,IAAI;cAACkB,KAAK,EAAE;gBAAEmB,MAAM,EAAE;cAAO,CAAE;cAAArB,QAAA,gBAC9BR,OAAA,CAACC,KAAK;gBAAC+B,KAAK,EAAE,CAAE;gBAAAxB,QAAA,EAAC;cAAqB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9ChB,OAAA,CAACE,SAAS;gBAAAM,QAAA,EAAC;cAEX;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACZhB,OAAA,CAACP,KAAK;gBAAC8D,SAAS,EAAC,UAAU;gBAAA/C,QAAA,gBACzBR,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACL,mBAAmB;oBAACe,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAE4C,WAAW,EAAE;oBAAE;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,uBAAmB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9GhB,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACL,mBAAmB;oBAACe,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAE4C,WAAW,EAAE;oBAAE;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,wBAAoB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC/GhB,OAAA,CAACE,SAAS;kBAAAM,QAAA,gBAACR,OAAA,CAACL,mBAAmB;oBAACe,KAAK,EAAE;sBAAEE,KAAK,EAAE,SAAS;sBAAE4C,WAAW,EAAE;oBAAE;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qBAAiB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhB,OAAA;MAASS,SAAS,EAAC,SAAS;MAACC,KAAK,EAAE;QAAE+C,UAAU,EAAE,SAAS;QAAE7C,KAAK,EAAE;MAAO,CAAE;MAAAJ,QAAA,eAC3ER,OAAA;QAAKS,SAAS,EAAC,WAAW;QAACC,KAAK,EAAE;UAAEyB,SAAS,EAAE;QAAS,CAAE;QAAA3B,QAAA,gBACxDR,OAAA,CAACC,KAAK;UAAC+B,KAAK,EAAE,CAAE;UAACtB,KAAK,EAAE;YAAEE,KAAK,EAAE,MAAM;YAAEsB,YAAY,EAAE;UAAG,CAAE;UAAA1B,QAAA,EAAC;QAA4B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjGhB,OAAA,CAACE,SAAS;UAACQ,KAAK,EAAE;YAAEuB,QAAQ,EAAE,EAAE;YAAEC,YAAY,EAAE,EAAE;YAAEtB,KAAK,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAErE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZhB,OAAA,CAACX,MAAM;UAAC6B,IAAI,EAAC,OAAO;UAACR,KAAK,EAAE;YAAE+C,UAAU,EAAE,MAAM;YAAE7C,KAAK,EAAE;UAAU,CAAE;UAAAJ,QAAA,eACnER,OAAA,CAACb,IAAI;YAACgC,EAAE,EAAC,UAAU;YAAAX,QAAA,EAAC;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC0C,EAAA,GAhMItD,QAAkB;AAkMxB,eAAeA,QAAQ;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}